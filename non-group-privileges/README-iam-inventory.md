# AWS Organization IAM Users Inventory

This script provides a comprehensive inventory of ALL IAM users across your entire AWS Organization using cross-account roles.

## Overview

The `list-all-iam-users.sh` script will:

1. **Discover all accounts** in your AWS Organization
2. **Use cross-account roles** to access each account securely
3. **List all IAM users** in each account (not just failing ones)
4. **Generate detailed reports** with user information, policies, and groups
5. **Categorize users** by type (human, service accounts, etc.)
6. **Create multiple output formats** for analysis and compliance

## Prerequisites

1. **AWS CLI configured** with credentials for your management account
2. **Cross-account roles deployed** using the StackSet in `@cross-account-roles/`
3. **Organizations permissions** in the management account
4. **jq installed** for JSON processing

## Cross-Account Role Requirements

The script uses the `CrossAccountAuditRole` with:
- **Role Name**: `CrossAccountAuditRole`
- **External ID**: `DrataCleanup2025`
- **Required Permissions**: IAM read permissions (included in your current template)

## Usage

### Basic Usage

Run from your management account:

```bash
cd non-group-privileges
./list-all-iam-users.sh
```

### What It Does

1. **Validates credentials** and Organizations access
2. **Retrieves all accounts** from AWS Organizations
3. **Processes each active account**:
   - Uses current credentials for management account
   - Assumes cross-account role for other accounts
   - Lists all IAM users in the account
   - Analyzes user policies, groups, and metadata
4. **Generates comprehensive reports**

## Output Files

All reports are saved to `org-iam-inventory-reports/` with timestamps:

### 1. Detailed Report (`org-iam-users-detailed-TIMESTAMP.txt`)
- Complete user information for each account
- User ARNs, creation dates, policies, groups
- Detailed analysis and categorization

### 2. Summary CSV (`org-iam-users-summary-TIMESTAMP.csv`)
- Structured data for spreadsheet analysis
- Columns: Account ID, Account Name, User Name, User Type, Policies, Groups, etc.
- Perfect for filtering and pivot tables

### 3. Statistics (`org-iam-users-stats-TIMESTAMP.txt`)
- High-level summary of findings
- Account processing status
- User type breakdown
- Next steps recommendations

### 4. Accounts List (`org-accounts-TIMESTAMP.txt`)
- List of all accounts in your organization
- Account IDs, names, and status

## User Categorization

The script automatically categorizes users:

- **github-service**: Users starting with `github-`
- **keycloak-service**: Users starting with `keycloak-`
- **terraform-service**: Users containing `terraform`
- **ml-service**: Users starting with `clearml-`
- **vonage-service**: Users starting with `tokbox_vonage`
- **drp-service**: Users starting with `drp-`
- **service-account**: Users ending with `-sa`
- **human-user**: Users containing dots (e.g., `john.doe`)
- **other**: All other users

## Error Handling

The script handles various error conditions gracefully:

- **Role assumption failures**: Logs error and continues with next account
- **IAM access issues**: Records failure and continues
- **Network timeouts**: Retries and logs issues
- **Invalid account IDs**: Skips and logs warnings

## Security Considerations

- Uses **external ID** for additional security
- **Temporary credentials** for cross-account access
- **Least privilege** IAM permissions
- **No persistent credential storage**

## Example Output

```
=== AWS Organization IAM Users Inventory ===
Starting inventory at 2025-01-30 10:30:00
Reports will be saved to: org-iam-inventory-reports/

1. Checking AWS credentials and Organizations access...
✅ AWS credentials are working
Management Account ID: ************
✅ AWS Organizations access confirmed

2. Retrieving all accounts from AWS Organizations...
Found 56 total accounts (56 active)

3. Processing accounts and listing IAM users...
🔍 Processing Account: ************ (Management) - Status: ACTIVE
  (Management account - using current credentials)
📋 Listing IAM users in Account: ************ (Management) - Status: ACTIVE
    Found 15 IAM users
      Analyzing user: admin.user
      Analyzing user: terraform-sa
      ...

=== Inventory Complete ===
Total accounts processed: 54/56
Total IAM users found: 342

Reports generated:
  - Detailed report: org-iam-inventory-reports/org-iam-users-detailed-********-103000.txt
  - Summary CSV: org-iam-inventory-reports/org-iam-users-summary-********-103000.csv
  - Statistics: org-iam-inventory-reports/org-iam-users-stats-********-103000.txt
  - Accounts list: org-iam-inventory-reports/org-accounts-********-103000.txt
```

## Troubleshooting

### Common Issues

1. **"Cannot access AWS Organizations"**
   - Ensure you're running from the management account
   - Check Organizations permissions

2. **"Failed to assume role"**
   - Verify cross-account roles are deployed
   - Check role name and external ID match
   - Ensure IAM permissions are correct

3. **"No IAM users found"**
   - Some accounts may legitimately have no IAM users
   - Check if account has IAM service enabled

### Debug Mode

For troubleshooting, you can examine the detailed report file which includes:
- Role assumption attempts and results
- IAM API call responses
- Error messages and stack traces

## Integration with Other Scripts

This inventory script complements other scripts in this repository:

- **analyze-iam-users.sh**: Analyzes specific failing users from DRATA
- **remediate-group-access.sh**: Migrates users to group-based access
- **validate-group-access.sh**: Validates group-based access implementation

## Next Steps

After running the inventory:

1. **Review the summary CSV** for patterns and compliance issues
2. **Identify high-risk users** with excessive permissions
3. **Plan group-based access control** migration
4. **Use findings for IAM governance** improvements
5. **Schedule regular inventories** for ongoing compliance
