#!/bin/bash

# AWS Organization IAM Users Inventory Script
# This script lists ALL IAM users across your entire AWS Organization
# using cross-account roles for access

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
TIMESTAMP=$(date +%Y%m%d-%H%M%S)
REPORTS_DIR="${SCRIPT_DIR}/org-iam-inventory-reports"
REPORT_FILE="${REPORTS_DIR}/org-iam-users-detailed-${TIMESTAMP}.txt"
SUMMARY_FILE="${REPORTS_DIR}/org-iam-users-summary-${TIMESTAMP}.csv"
STATS_FILE="${REPORTS_DIR}/org-iam-users-stats-${TIMESTAMP}.txt"
ACCOUNTS_FILE="${REPORTS_DIR}/org-accounts-${TIMESTAMP}.txt"

# Cross-account role configuration
CROSS_ACCOUNT_ROLE_NAME="CrossAccountAuditRole"
EXTERNAL_ID="DrataCleanup2025"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Create reports directory
mkdir -p "${REPORTS_DIR}"

# Initialize counters
total_users=0
total_accounts=0
successful_accounts=0
failed_accounts=0
service_accounts=0
human_users=0
github_users=0
keycloak_users=0
terraform_users=0
other_users=0

echo -e "${BLUE}=== AWS Organization IAM Users Inventory ===${NC}"
echo "Starting inventory at $(date)"
echo "Reports will be saved to: ${REPORTS_DIR}"
echo

# Function to log messages
log() {
    echo -e "$1"
    echo -e "$1" | sed 's/\x1b\[[0-9;]*m//g' >> "${REPORT_FILE}"
}

# Check AWS credentials and Organizations access
echo -e "${BLUE}1. Checking AWS credentials and Organizations access...${NC}"
MANAGEMENT_ACCOUNT_ID=$(aws sts get-caller-identity --query 'Account' --output text 2>&1)
IDENTITY_EXIT_CODE=$?

if [ $IDENTITY_EXIT_CODE -ne 0 ]; then
    echo "❌ Failed to get AWS identity"
    echo "Error: $MANAGEMENT_ACCOUNT_ID"
    echo "Please check AWS CLI configuration and credentials"
    exit 1
fi

echo "✅ AWS credentials are working"
echo "Management Account ID: $MANAGEMENT_ACCOUNT_ID"

# Verify Organizations access
if ! aws organizations describe-organization >/dev/null 2>&1; then
    echo -e "${RED}❌ Cannot access AWS Organizations${NC}"
    echo "Make sure you're running from the management account with Organizations permissions"
    exit 1
fi

echo -e "${GREEN}✅ AWS Organizations access confirmed${NC}"

# Initialize report files
cat > "${REPORT_FILE}" << EOF
AWS Organization IAM Users Inventory Report
Generated: $(date)
Management Account: ${MANAGEMENT_ACCOUNT_ID}

This report contains ALL IAM users across the entire AWS Organization.

=== INVENTORY RESULTS ===

EOF

# Initialize summary CSV
cat > "${SUMMARY_FILE}" << EOF
Account ID,Account Name,Account Status,User Name,User ARN,User Type,Created Date,Inline Policies,Attached Policies,Groups,Last Activity,Access Status
EOF

# Function to categorize user type
categorize_user() {
    local username="$1"

    if [[ "$username" =~ ^github- ]]; then
        echo "github-service"
    elif [[ "$username" =~ ^keycloak- ]]; then
        echo "keycloak-service"
    elif [[ "$username" =~ terraform ]]; then
        echo "terraform-service"
    elif [[ "$username" =~ ^clearml- ]]; then
        echo "ml-service"
    elif [[ "$username" =~ ^tokbox_vonage ]]; then
        echo "vonage-service"
    elif [[ "$username" =~ ^drp- ]]; then
        echo "drp-service"
    elif [[ "$username" =~ -sa$ ]]; then
        echo "service-account"
    elif [[ "$username" =~ \. ]]; then
        echo "human-user"
    else
        echo "other"
    fi
}

# Function to assume cross-account role
assume_role() {
    local account_id=$1
    local role_arn="arn:aws:iam::${account_id}:role/${CROSS_ACCOUNT_ROLE_NAME}"

    log "    Attempting to assume role: $role_arn"

    # Assume the role and capture both stdout and stderr
    local assume_result=$(aws sts assume-role \
        --role-arn "$role_arn" \
        --role-session-name "OrgIAMInventory-$(date +%s)" \
        --external-id "$EXTERNAL_ID" \
        --duration-seconds 3600 2>&1)

    local exit_code=$?

    if [ $exit_code -ne 0 ]; then
        log "    ❌ Role assumption failed with exit code: $exit_code"
        log "    Error details: $assume_result"
        return 1
    fi

    # Validate JSON response
    if ! echo "$assume_result" | jq -e '.Credentials' >/dev/null 2>&1; then
        log "    ❌ Invalid JSON response from assume-role"
        return 1
    fi

    # Export credentials
    export AWS_ACCESS_KEY_ID=$(echo "$assume_result" | jq -r '.Credentials.AccessKeyId')
    export AWS_SECRET_ACCESS_KEY=$(echo "$assume_result" | jq -r '.Credentials.SecretAccessKey')
    export AWS_SESSION_TOKEN=$(echo "$assume_result" | jq -r '.Credentials.SessionToken')

    log "    ✅ Successfully assumed role"
    return 0
}

# Function to reset credentials to original
reset_credentials() {
    unset AWS_ACCESS_KEY_ID AWS_SECRET_ACCESS_KEY AWS_SESSION_TOKEN
}

# Function to get last activity for a user (simplified)
get_last_activity() {
    local username="$1"
    # This is a simplified check - you could enhance this with CloudTrail data
    local access_keys=$(aws iam list-access-keys --user-name "$username" 2>/dev/null | jq -r '.AccessKeyMetadata[0].CreateDate // "No keys"')
    echo "$access_keys"
}

# Function to list and analyze IAM users in an account
list_account_users() {
    local account_id="$1"
    local account_name="$2"
    local account_status="$3"

    log ""
    log "${YELLOW}📋 Listing IAM users in Account: $account_id ($account_name) - Status: $account_status${NC}"

    # Get list of IAM users
    local users_json
    if users_json=$(aws iam list-users --output json 2>/dev/null); then
        local user_count=$(echo "$users_json" | jq '.Users | length')
        log "    Found $user_count IAM users"

        if [ "$user_count" -eq 0 ]; then
            log "    No IAM users found in this account"
            echo "${account_id},\"${account_name}\",${account_status},NO_USERS,N/A,N/A,N/A,0,0,\"\",N/A,SUCCESS" >> "${SUMMARY_FILE}"
            return 0
        fi

        # Process each user
        echo "$users_json" | jq -r '.Users[] | @base64' | while IFS= read -r user_data; do
            local user_info=$(echo "$user_data" | base64 --decode)
            local username=$(echo "$user_info" | jq -r '.UserName')
            local user_arn=$(echo "$user_info" | jq -r '.Arn')
            local create_date=$(echo "$user_info" | jq -r '.CreateDate')

            log "      Analyzing user: $username"

            # Get inline policies count
            local inline_policies=0
            if inline_count=$(aws iam list-user-policies --user-name "$username" 2>/dev/null | jq '.PolicyNames | length'); then
                inline_policies=$inline_count
            fi

            # Get attached managed policies count
            local attached_policies=0
            if attached_count=$(aws iam list-attached-user-policies --user-name "$username" 2>/dev/null | jq '.AttachedPolicies | length'); then
                attached_policies=$attached_count
            fi

            # Get groups
            local groups=""
            if group_list=$(aws iam get-groups-for-user --user-name "$username" 2>/dev/null | jq -r '.Groups[].GroupName' | tr '\n' ';' | sed 's/;$//'); then
                groups="$group_list"
            fi

            # Get last activity (simplified)
            local last_activity=$(get_last_activity "$username")

            # Categorize user
            local user_type=$(categorize_user "$username")

            # Write detailed info to report
            cat >> "${REPORT_FILE}" << EOF

--- User: ${username} (${user_type}) ---
Account: ${account_id} - ${account_name}
ARN: ${user_arn}
Created: ${create_date}
Inline Policies: ${inline_policies}
Attached Policies: ${attached_policies}
Groups: ${groups:-"None"}
Last Activity: ${last_activity}

EOF

            # Write to summary CSV
            echo "${account_id},\"${account_name}\",${account_status},${username},\"${user_arn}\",${user_type},\"${create_date}\",${inline_policies},${attached_policies},\"${groups}\",\"${last_activity}\",SUCCESS" >> "${SUMMARY_FILE}"

            # Update counters
            ((total_users++))
            case "$user_type" in
                "github-service") ((github_users++)) ;;
                "keycloak-service") ((keycloak_users++)) ;;
                "terraform-service") ((terraform_users++)) ;;
                "service-account") ((service_accounts++)) ;;
                "human-user") ((human_users++)) ;;
                *) ((other_users++)) ;;
            esac
        done

        log "    ✅ Successfully processed $user_count users"

    else
        log "    ❌ Failed to list IAM users"
        echo "${account_id},\"${account_name}\",${account_status},ERROR,N/A,N/A,N/A,0,0,\"\",N/A,FAILED" >> "${SUMMARY_FILE}"
        return 1
    fi
}

# Function to process a single account
process_account() {
    local account_id="$1"
    local account_name="$2"
    local account_status="$3"

    log ""
    log "${YELLOW}🔍 Processing Account: $account_id ($account_name) - Status: $account_status${NC}"

    # Skip management account (no need to assume role)
    if [ "$account_id" = "$MANAGEMENT_ACCOUNT_ID" ]; then
        log "  (Management account - using current credentials)"
        list_account_users "$account_id" "$account_name" "$account_status"
        ((successful_accounts++))
    else
        log "  Assuming cross-account role..."
        if assume_role "$account_id"; then
            log "  ✅ Successfully assumed role"
            list_account_users "$account_id" "$account_name" "$account_status"
            reset_credentials
            ((successful_accounts++))
        else
            log "  ❌ Failed to assume role - skipping account"
            ((failed_accounts++))
            echo "${account_id},\"${account_name}\",${account_status},ROLE_FAILED,N/A,N/A,N/A,0,0,\"\",N/A,NO_ACCESS" >> "${SUMMARY_FILE}"
            return 1
        fi
    fi
}

# Get all accounts in the organization
echo ""
echo -e "${BLUE}2. Retrieving all accounts from AWS Organizations...${NC}"

ACCOUNTS_JSON=$(aws organizations list-accounts --output json 2>/dev/null)
if [ $? -ne 0 ]; then
    echo -e "${RED}❌ Failed to retrieve accounts from AWS Organizations${NC}"
    exit 1
fi

# Save accounts list for reference
echo "$ACCOUNTS_JSON" | jq -r '.Accounts[] | "\(.Id),\"\(.Name)\",\(.Status)"' > "${ACCOUNTS_FILE}"

total_accounts=$(echo "$ACCOUNTS_JSON" | jq '.Accounts | length')
active_accounts=$(echo "$ACCOUNTS_JSON" | jq '.Accounts | map(select(.Status == "ACTIVE")) | length')

echo "Found $total_accounts total accounts ($active_accounts active)"
echo "Accounts list saved to: ${ACCOUNTS_FILE}"

# Process each active account
echo ""
echo -e "${BLUE}3. Processing accounts and listing IAM users...${NC}"

echo "$ACCOUNTS_JSON" | jq -r '.Accounts[] | select(.Status == "ACTIVE") | "\(.Id)|\(.Name)|\(.Status)"' | while IFS='|' read -r account_id account_name account_status; do
    # Skip empty account IDs
    if [ -z "$account_id" ]; then
        continue
    fi

    # Validate account ID format (12 digits)
    if [[ ! "$account_id" =~ ^[0-9]{12}$ ]]; then
        log "⚠️  Skipping invalid account ID: '$account_id'"
        continue
    fi

    # Process the account (don't exit on failure)
    process_account "$account_id" "$account_name" "$account_status" || {
        log "⚠️  Account $account_id processing failed, continuing with next account..."
    }
done

# Generate final statistics
echo ""
echo -e "${BLUE}4. Generating final statistics...${NC}"

cat > "${STATS_FILE}" << EOF
AWS Organization IAM Users Inventory Statistics
Generated: $(date)
Management Account: ${MANAGEMENT_ACCOUNT_ID}

=== ACCOUNT SUMMARY ===
Total Accounts in Organization: ${total_accounts}
Active Accounts: ${active_accounts}
Successfully Processed: ${successful_accounts}
Failed to Access: ${failed_accounts}

=== USER SUMMARY ===
Total IAM Users Found: ${total_users}

=== USER TYPE BREAKDOWN ===
GitHub Service Accounts: ${github_users}
Keycloak Service Accounts: ${keycloak_users}
Terraform Service Accounts: ${terraform_users}
Other Service Accounts: ${service_accounts}
Human Users: ${human_users}
Other/Unknown: ${other_users}

=== FILES GENERATED ===
Detailed Report: ${REPORT_FILE}
Summary CSV: ${SUMMARY_FILE}
Statistics: ${STATS_FILE}
Accounts List: ${ACCOUNTS_FILE}

=== NEXT STEPS ===
1. Review the detailed report for complete user information
2. Analyze the summary CSV for patterns and compliance issues
3. Use the data for IAM governance and security reviews
4. Consider group-based access control migration where appropriate

EOF

echo -e "${GREEN}=== Inventory Complete ===${NC}"
echo "Total accounts processed: ${successful_accounts}/${active_accounts}"
echo "Total IAM users found: ${total_users}"
echo ""
echo "Reports generated:"
echo "  - Detailed report: ${REPORT_FILE}"
echo "  - Summary CSV: ${SUMMARY_FILE}"
echo "  - Statistics: ${STATS_FILE}"
echo "  - Accounts list: ${ACCOUNTS_FILE}"
echo ""
echo -e "${YELLOW}Next steps:${NC}"
echo "1. Review the reports to understand your IAM landscape"
echo "2. Identify security risks and compliance issues"
echo "3. Plan IAM governance improvements"
echo "4. Consider implementing group-based access control"

# Final summary to console
echo ""
echo -e "${BLUE}=== FINAL SUMMARY ===${NC}"
echo "Accounts: ${successful_accounts} processed, ${failed_accounts} failed"
echo "Users by type:"
echo "  - Human users: ${human_users}"
echo "  - Service accounts: ${service_accounts}"
echo "  - GitHub service: ${github_users}"
echo "  - Keycloak service: ${keycloak_users}"
echo "  - Terraform service: ${terraform_users}"
echo "  - Other/Unknown: ${other_users}"
echo "Total users: ${total_users}"
