AWS Organization IAM Users Inventory Report
Generated: Mon Jun  2 13:37:39 CST 2025
Management Account: ************

This report contains ALL IAM users across the entire AWS Organization.

=== INVENTORY RESULTS ===


🔍 Processing Account: ************ (<EMAIL>) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountVPCManagementRole
    External ID: DrataCleanup2025
    ✅ Successfully assumed role and validated credentials
  ✅ Successfully assumed role

📋 Listing IAM users in Account: ************ (<EMAIL>) - Status: ACTIVE
    Found 3 IAM users
      Analyzing user: github-terraform-serv-acc

--- User: github-terraform-serv-acc (github-service) ---
Account: ************ - <EMAIL>
ARN: arn:aws:iam::************:user/github-terraform-serv-acc
Created: 2024-08-14T01:26:28+00:00
Inline Policies: 0
Attached Policies: 2
Groups: None
Last Activity: 2024-08-14T01:30:10+00:00

      Analyzing user: keycloak-sxdb5pw5-sa

--- User: keycloak-sxdb5pw5-sa (keycloak-service) ---
Account: ************ - <EMAIL>
ARN: arn:aws:iam::************:user/keycloak-sxdb5pw5-sa
Created: 2024-08-20T06:26:29+00:00
Inline Policies: 0
Attached Policies: 1
Groups: None
Last Activity: 2024-08-20T06:26:29+00:00

      Analyzing user: tokbox_vonage_integration

--- User: tokbox_vonage_integration (vonage-service) ---
Account: ************ - <EMAIL>
ARN: arn:aws:iam::************:user/tokbox_vonage_integration
Created: 2024-08-19T07:26:32+00:00
Inline Policies: 0
Attached Policies: 1
Groups: None
Last Activity: 2024-08-19T07:37:29+00:00

    ✅ Successfully processed 3 users
⚠️  Account ************ processing failed, continuing with next account...

🔍 Processing Account: ************ (Certificate Authorities) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountVPCManagementRole
    External ID: DrataCleanup2025
    ✅ Successfully assumed role and validated credentials
  ✅ Successfully assumed role

📋 Listing IAM users in Account: ************ (Certificate Authorities) - Status: ACTIVE
    Found 0 IAM users
    No IAM users found in this account

🔍 Processing Account: ************ (incode-bridge) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountVPCManagementRole
    External ID: DrataCleanup2025
    ✅ Successfully assumed role and validated credentials
  ✅ Successfully assumed role

📋 Listing IAM users in Account: ************ (incode-bridge) - Status: ACTIVE
    Found 0 IAM users
    No IAM users found in this account

🔍 Processing Account: ************ (Incode Technologies T2 9688) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountVPCManagementRole
    External ID: DrataCleanup2025
    ✅ Successfully assumed role and validated credentials
  ✅ Successfully assumed role

📋 Listing IAM users in Account: ************ (Incode Technologies T2 9688) - Status: ACTIVE
    Found 21 IAM users
      Analyzing user: backend-monitoring-instance

--- User: backend-monitoring-instance (other) ---
Account: ************ - Incode Technologies T2 9688
ARN: arn:aws:iam::************:user/backend-monitoring-instance
Created: 2024-01-25T14:04:18+00:00
Inline Policies: 0
Attached Policies: 2
Groups: None
Last Activity: 2024-01-25T14:06:17+00:00

      Analyzing user: business-intelligence-analytics

--- User: business-intelligence-analytics (other) ---
Account: ************ - Incode Technologies T2 9688
ARN: arn:aws:iam::************:user/business-intelligence-analytics
Created: 2025-05-26T21:59:43+00:00
Inline Policies: 0
Attached Policies: 2
Groups: None
Last Activity: 2025-05-26T22:06:10+00:00

      Analyzing user: doug.carroll

--- User: doug.carroll (human-user) ---
Account: ************ - Incode Technologies T2 9688
ARN: arn:aws:iam::************:user/doug.carroll
Created: 2022-02-03T04:39:30+00:00
Inline Policies: 0
Attached Policies: 0
Groups: None
Last Activity: No keys

      Analyzing user: drp-failover-manager

--- User: drp-failover-manager (drp-service) ---
Account: ************ - Incode Technologies T2 9688
ARN: arn:aws:iam::************:user/drp-failover-manager
Created: 2025-02-22T21:51:45+00:00
Inline Policies: 0
Attached Policies: 1
Groups: None
Last Activity: 2025-02-22T21:59:09+00:00

      Analyzing user: federico.deluna

--- User: federico.deluna (human-user) ---
Account: ************ - Incode Technologies T2 9688
ARN: arn:aws:iam::************:user/federico.deluna
Created: 2022-01-14T07:02:47+00:00
Inline Policies: 0
Attached Policies: 0
Groups: None
Last Activity: 2022-04-29T20:46:26+00:00

      Analyzing user: github-actions-id-ocr

--- User: github-actions-id-ocr (github-service) ---
Account: ************ - Incode Technologies T2 9688
ARN: arn:aws:iam::************:user/github-actions-id-ocr
Created: 2025-03-18T15:08:57+00:00
Inline Policies: 1
Attached Policies: 0
Groups: None
Last Activity: 2025-03-18T15:14:10+00:00

      Analyzing user: github-actions-keycloak-ecr

--- User: github-actions-keycloak-ecr (github-service) ---
Account: ************ - Incode Technologies T2 9688
ARN: arn:aws:iam::************:user/github-actions-keycloak-ecr
Created: 2023-01-19T16:13:34+00:00
Inline Policies: 0
Attached Policies: 1
Groups: None
Last Activity: 2023-01-19T16:17:46+00:00

      Analyzing user: github-actions-ml-wasm-kit

--- User: github-actions-ml-wasm-kit (github-service) ---
Account: ************ - Incode Technologies T2 9688
ARN: arn:aws:iam::************:user/github-actions-ml-wasm-kit
Created: 2023-11-08T02:10:41+00:00
Inline Policies: 0
Attached Policies: 1
Groups: None
Last Activity: 2023-11-08T02:11:21+00:00

      Analyzing user: github-incode-api-official

--- User: github-incode-api-official (github-service) ---
Account: ************ - Incode Technologies T2 9688
ARN: arn:aws:iam::************:user/github-incode-api-official
Created: 2024-02-07T00:43:52+00:00
Inline Policies: 0
Attached Policies: 1
Groups: None
Last Activity: 2024-02-07T00:44:27+00:00

      Analyzing user: github-terraform-serv-acc

--- User: github-terraform-serv-acc (github-service) ---
Account: ************ - Incode Technologies T2 9688
ARN: arn:aws:iam::************:user/github-terraform-serv-acc
Created: 2023-03-01T16:45:47+00:00
Inline Policies: 0
Attached Policies: 2
Groups: None
Last Activity: 2023-03-01T16:46:34+00:00

      Analyzing user: incode-s3

--- User: incode-s3 (other) ---
Account: ************ - Incode Technologies T2 9688
ARN: arn:aws:iam::************:user/incode-s3
Created: 2017-09-23T01:07:12+00:00
Inline Policies: 0
Attached Policies: 1
Groups: None
Last Activity: 2020-10-19T09:50:40+00:00

      Analyzing user: jose.manuel.martinez

--- User: jose.manuel.martinez (human-user) ---
Account: ************ - Incode Technologies T2 9688
ARN: arn:aws:iam::************:user/jose.manuel.martinez
Created: 2021-12-03T03:56:07+00:00
Inline Policies: 0
Attached Policies: 0
Groups: None
Last Activity: 2022-12-15T16:59:42+00:00

      Analyzing user: konstantin.moiseev

--- User: konstantin.moiseev (human-user) ---
Account: ************ - Incode Technologies T2 9688
ARN: arn:aws:iam::************:user/konstantin.moiseev
Created: 2022-07-05T16:29:34+00:00
Inline Policies: 0
Attached Policies: 0
Groups: None
Last Activity: 2022-07-19T09:34:04+00:00

      Analyzing user: label-studio-admin

--- User: label-studio-admin (other) ---
Account: ************ - Incode Technologies T2 9688
ARN: arn:aws:iam::************:user/label-studio-admin
Created: 2025-01-12T21:24:23+00:00
Inline Policies: 0
Attached Policies: 1
Groups: None
Last Activity: 2025-01-12T21:26:45+00:00

      Analyzing user: label-studio-task-user

--- User: label-studio-task-user (other) ---
Account: ************ - Incode Technologies T2 9688
ARN: arn:aws:iam::************:user/label-studio-task-user
Created: 2025-01-12T21:08:35+00:00
Inline Policies: 0
Attached Policies: 1
Groups: None
Last Activity: 2025-01-12T21:14:23+00:00

      Analyzing user: lazar.slavkovic

--- User: lazar.slavkovic (human-user) ---
Account: ************ - Incode Technologies T2 9688
ARN: arn:aws:iam::************:user/lazar.slavkovic
Created: 2025-05-22T17:50:04+00:00
Inline Policies: 0
Attached Policies: 0
Groups: None
Last Activity: 2025-05-22T17:50:17+00:00

      Analyzing user: mario.dominguez

--- User: mario.dominguez (human-user) ---
Account: ************ - Incode Technologies T2 9688
ARN: arn:aws:iam::************:user/mario.dominguez
Created: 2022-08-31T07:37:00+00:00
Inline Policies: 0
Attached Policies: 1
Groups: None
Last Activity: 2022-08-31T21:53:18+00:00

      Analyzing user: marygold537

--- User: marygold537 (other) ---
Account: ************ - Incode Technologies T2 9688
ARN: arn:aws:iam::************:user/marygold537
Created: 2025-04-25T19:13:27+00:00
Inline Policies: 0
Attached Policies: 1
Groups: None
Last Activity: 2025-04-25T19:21:27+00:00

      Analyzing user: papertrail-user-01

--- User: papertrail-user-01 (other) ---
Account: ************ - Incode Technologies T2 9688
ARN: arn:aws:iam::************:user/papertrail-user-01
Created: 2022-07-27T02:00:28+00:00
Inline Policies: 0
Attached Policies: 0
Groups: None
Last Activity: 2022-07-27T02:00:29+00:00

      Analyzing user: s3Full

--- User: s3Full (other) ---
Account: ************ - Incode Technologies T2 9688
ARN: arn:aws:iam::************:user/s3Full
Created: 2024-04-18T23:50:37+00:00
Inline Policies: 0
Attached Policies: 0
Groups: None
Last Activity: 2024-04-18T23:51:03+00:00

      Analyzing user: tokbox_vonage_integration_saas-eu

--- User: tokbox_vonage_integration_saas-eu (vonage-service) ---
Account: ************ - Incode Technologies T2 9688
ARN: arn:aws:iam::************:user/tokbox_vonage_integration_saas-eu
Created: 2024-01-11T13:31:59+00:00
Inline Policies: 0
Attached Policies: 1
Groups: None
Last Activity: 2024-01-11T13:32:25+00:00

    ✅ Successfully processed 21 users

🔍 Processing Account: ************ (<EMAIL>) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountVPCManagementRole
    External ID: DrataCleanup2025
    ✅ Successfully assumed role and validated credentials
  ✅ Successfully assumed role

📋 Listing IAM users in Account: ************ (<EMAIL>) - Status: ACTIVE
    Found 1 IAM users
      Analyzing user: terraform-saas-uae

--- User: terraform-saas-uae (terraform-service) ---
Account: ************ - <EMAIL>
ARN: arn:aws:iam::************:user/terraform-saas-uae
Created: 2025-01-15T13:47:22+00:00
Inline Policies: 0
Attached Policies: 1
Groups: None
Last Activity: 2025-01-15T13:49:29+00:00

    ✅ Successfully processed 1 users

🔍 Processing Account: ************ (aws-account-customer-success-tooling) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountVPCManagementRole
    External ID: DrataCleanup2025
    ✅ Successfully assumed role and validated credentials
  ✅ Successfully assumed role

📋 Listing IAM users in Account: ************ (aws-account-customer-success-tooling) - Status: ACTIVE
    Found 3 IAM users
      Analyzing user: cw-from-sessions-user

--- User: cw-from-sessions-user (other) ---
Account: ************ - aws-account-customer-success-tooling
ARN: arn:aws:iam::************:user/cw-from-sessions-user
Created: 2024-11-29T00:18:04+00:00
Inline Policies: 0
Attached Policies: 0
Groups: None
Last Activity: 2024-11-29T00:23:03+00:00

      Analyzing user: incodeMigrationUser

--- User: incodeMigrationUser (other) ---
Account: ************ - aws-account-customer-success-tooling
ARN: arn:aws:iam::************:user/incodeMigrationUser
Created: 2025-01-15T17:57:39+00:00
Inline Policies: 2
Attached Policies: 2
Groups: None
Last Activity: 2025-02-18T05:59:41+00:00

      Analyzing user: se-tooling-cicd-pipeline-user

--- User: se-tooling-cicd-pipeline-user (other) ---
Account: ************ - aws-account-customer-success-tooling
ARN: arn:aws:iam::************:user/se-tooling-cicd-pipeline-user
Created: 2025-03-04T00:31:38+00:00
Inline Policies: 0
Attached Policies: 2
Groups: None
Last Activity: 2025-03-04T00:34:21+00:00

    ✅ Successfully processed 3 users

🔍 Processing Account: ************ (incode_logging) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountVPCManagementRole
    External ID: DrataCleanup2025
    ✅ Successfully assumed role and validated credentials
  ✅ Successfully assumed role

📋 Listing IAM users in Account: ************ (incode_logging) - Status: ACTIVE
    Found 0 IAM users
    No IAM users found in this account

🔍 Processing Account: ************ (AWS-account-networking) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountVPCManagementRole
    External ID: DrataCleanup2025
    ✅ Successfully assumed role and validated credentials
  ✅ Successfully assumed role

📋 Listing IAM users in Account: ************ (AWS-account-networking) - Status: ACTIVE
    Found 0 IAM users
    No IAM users found in this account

🔍 Processing Account: ************ (AWS-account-operations) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountVPCManagementRole
    External ID: DrataCleanup2025
    ✅ Successfully assumed role and validated credentials
  ✅ Successfully assumed role

📋 Listing IAM users in Account: ************ (AWS-account-operations) - Status: ACTIVE
    Found 0 IAM users
    No IAM users found in this account

🔍 Processing Account: ************ (AWS Account Canada SAAS) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountVPCManagementRole
    External ID: DrataCleanup2025
    ✅ Successfully assumed role and validated credentials
  ✅ Successfully assumed role

📋 Listing IAM users in Account: ************ (AWS Account Canada SAAS) - Status: ACTIVE
    Found 5 IAM users
      Analyzing user: github-actions-web

--- User: github-actions-web (github-service) ---
Account: ************ - AWS Account Canada SAAS
ARN: arn:aws:iam::************:user/github-actions-web
Created: 2023-11-07T16:22:18+00:00
Inline Policies: 0
Attached Policies: 1
Groups: None
Last Activity: 2023-11-07T16:22:45+00:00

      Analyzing user: github-terraform-serv-acc

--- User: github-terraform-serv-acc (github-service) ---
Account: ************ - AWS Account Canada SAAS
ARN: arn:aws:iam::************:user/github-terraform-serv-acc
Created: 2025-04-25T05:35:57+00:00
Inline Policies: 0
Attached Policies: 2
Groups: None
Last Activity: No keys

      Analyzing user: IncodeuserServiceSAAS

--- User: IncodeuserServiceSAAS (other) ---
Account: ************ - AWS Account Canada SAAS
ARN: arn:aws:iam::************:user/IncodeuserServiceSAAS
Created: 2023-10-23T21:05:42+00:00
Inline Policies: 0
Attached Policies: 3
Groups: None
Last Activity: 2023-10-23T21:07:48+00:00

      Analyzing user: ServiceUserAccountInfoInfo

--- User: ServiceUserAccountInfoInfo (other) ---
Account: ************ - AWS Account Canada SAAS
ARN: arn:aws:iam::************:user/ServiceUserAccountInfoInfo
Created: 2023-10-19T23:18:10+00:00
Inline Policies: 0
Attached Policies: 1
Groups: None
Last Activity: 2023-10-19T23:18:49+00:00

      Analyzing user: tokbox_vonage_integrationCanadaSAAS

--- User: tokbox_vonage_integrationCanadaSAAS (vonage-service) ---
Account: ************ - AWS Account Canada SAAS
ARN: arn:aws:iam::************:user/tokbox_vonage_integrationCanadaSAAS
Created: 2023-10-24T04:56:30+00:00
Inline Policies: 0
Attached Policies: 1
Groups: None
Last Activity: 2023-10-24T04:56:53+00:00

    ✅ Successfully processed 5 users

🔍 Processing Account: ************ (Incode Technologies T1  4717) - Status: ACTIVE
  (Management account - using current credentials)

📋 Listing IAM users in Account: ************ (Incode Technologies T1  4717) - Status: ACTIVE
    Found 0 IAM users
    No IAM users found in this account

🔍 Processing Account: ************ (Operations) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountVPCManagementRole
    External ID: DrataCleanup2025
    ✅ Successfully assumed role and validated credentials
  ✅ Successfully assumed role

📋 Listing IAM users in Account: ************ (Operations) - Status: ACTIVE
    Found 7 IAM users
      Analyzing user: gitlab

--- User: gitlab (other) ---
Account: ************ - Operations
ARN: arn:aws:iam::************:user/gitlab
Created: 2021-08-30T15:16:58+00:00
Inline Policies: 1
Attached Policies: 1
Groups: None
Last Activity: 2023-03-01T17:43:36+00:00

      Analyzing user: gitlab-aws-automation

--- User: gitlab-aws-automation (other) ---
Account: ************ - Operations
ARN: arn:aws:iam::************:user/gitlab-aws-automation
Created: 2022-05-26T13:31:55+00:00
Inline Policies: 0
Attached Policies: 0
Groups: None
Last Activity: 2022-05-26T13:31:56+00:00

      Analyzing user: gitlab-ci

--- User: gitlab-ci (other) ---
Account: ************ - Operations
ARN: arn:aws:iam::************:user/gitlab-ci
Created: 2023-09-07T05:24:47+00:00
Inline Policies: 0
Attached Policies: 1
Groups: None
Last Activity: 2023-09-07T05:25:01+00:00

      Analyzing user: gitlab-smtp-user

--- User: gitlab-smtp-user (other) ---
Account: ************ - Operations
ARN: arn:aws:iam::************:user/gitlab-smtp-user
Created: 2024-05-25T09:45:29+00:00
Inline Policies: 1
Attached Policies: 0
Groups: None
Last Activity: 2024-05-25T09:45:29+00:00

      Analyzing user: metamap-security-team

--- User: metamap-security-team (other) ---
Account: ************ - Operations
ARN: arn:aws:iam::************:user/metamap-security-team
Created: 2023-07-25T12:43:27+00:00
Inline Policies: 0
Attached Policies: 1
Groups: None
Last Activity: 2023-11-06T14:09:26+00:00

      Analyzing user: ses-smtp-user.gitlab

--- User: ses-smtp-user.gitlab (human-user) ---
Account: ************ - Operations
ARN: arn:aws:iam::************:user/ses-smtp-user.gitlab
Created: 2023-11-11T06:49:57+00:00
Inline Policies: 1
Attached Policies: 0
Groups: None
Last Activity: 2023-11-11T06:49:58+00:00

      Analyzing user: s_hashicorp-vault

--- User: s_hashicorp-vault (other) ---
Account: ************ - Operations
ARN: arn:aws:iam::************:user/s_hashicorp-vault
Created: 2022-03-23T07:33:52+00:00
Inline Policies: 0
Attached Policies: 1
Groups: None
Last Activity: 2022-03-23T07:33:54+00:00

    ✅ Successfully processed 7 users

🔍 Processing Account: ************ (<EMAIL>) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountVPCManagementRole
    External ID: DrataCleanup2025
    ✅ Successfully assumed role and validated credentials
  ✅ Successfully assumed role

📋 Listing IAM users in Account: ************ (<EMAIL>) - Status: ACTIVE
    Found 2 IAM users
      Analyzing user: keycloak-3b4ss0st-sa

--- User: keycloak-3b4ss0st-sa (keycloak-service) ---
Account: ************ - <EMAIL>
ARN: arn:aws:iam::************:user/keycloak-3b4ss0st-sa
Created: 2024-08-29T07:59:41+00:00
Inline Policies: 0
Attached Policies: 1
Groups: None
Last Activity: 2024-08-29T07:59:55+00:00

      Analyzing user: keycloak-ast4obfh-sa

--- User: keycloak-ast4obfh-sa (keycloak-service) ---
Account: ************ - <EMAIL>
ARN: arn:aws:iam::************:user/keycloak-ast4obfh-sa
Created: 2024-07-31T15:07:40+00:00
Inline Policies: 0
Attached Policies: 1
Groups: None
Last Activity: 2024-07-31T15:07:56+00:00

    ✅ Successfully processed 2 users

🔍 Processing Account: ************ (Metamap Staging) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountVPCManagementRole
    External ID: DrataCleanup2025
    ✅ Successfully assumed role and validated credentials
  ✅ Successfully assumed role

📋 Listing IAM users in Account: ************ (Metamap Staging) - Status: ACTIVE
    Found 5 IAM users
      Analyzing user: dmitriy.dobrovitsky

--- User: dmitriy.dobrovitsky (human-user) ---
Account: ************ - Metamap Staging
ARN: arn:aws:iam::************:user/dmitriy.dobrovitsky
Created: 2025-02-12T14:07:03+00:00
Inline Policies: 0
Attached Policies: 2
Groups: None
Last Activity: No keys

      Analyzing user: metamap-frontend-service-user

--- User: metamap-frontend-service-user (other) ---
Account: ************ - Metamap Staging
ARN: arn:aws:iam::************:user/metamap-frontend-service-user
Created: 2022-06-24T07:43:45+00:00
Inline Policies: 0
Attached Policies: 1
Groups: None
Last Activity: 2022-06-30T14:13:14+00:00

      Analyzing user: metamap-security-team

--- User: metamap-security-team (other) ---
Account: ************ - Metamap Staging
ARN: arn:aws:iam::************:user/metamap-security-team
Created: 2023-07-25T12:27:43+00:00
Inline Policies: 0
Attached Policies: 0
Groups: None
Last Activity: 2023-11-06T14:11:50+00:00

      Analyzing user: temp-delete-me

--- User: temp-delete-me (other) ---
Account: ************ - Metamap Staging
ARN: arn:aws:iam::************:user/temp-delete-me
Created: 2022-04-23T21:59:40+00:00
Inline Policies: 0
Attached Policies: 1
Groups: None
Last Activity: 2022-04-23T21:59:42+00:00

      Analyzing user: test-shashi

--- User: test-shashi (other) ---
Account: ************ - Metamap Staging
ARN: arn:aws:iam::************:user/test-shashi
Created: 2024-03-20T11:35:01+00:00
Inline Policies: 1
Attached Policies: 0
Groups: None
Last Activity: 2024-03-20T11:35:57+00:00

    ✅ Successfully processed 5 users

🔍 Processing Account: ************ (AWS-account-development) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountVPCManagementRole
    External ID: DrataCleanup2025
    ✅ Successfully assumed role and validated credentials
  ✅ Successfully assumed role

📋 Listing IAM users in Account: ************ (AWS-account-development) - Status: ACTIVE
    Found 6 IAM users
      Analyzing user: github-actions-service-account

--- User: github-actions-service-account (github-service) ---
Account: ************ - AWS-account-development
ARN: arn:aws:iam::************:user/github-actions-service-account
Created: 2023-07-13T10:53:09+00:00
Inline Policies: 0
Attached Policies: 1
Groups: None
Last Activity: 2023-08-01T14:02:46+00:00

      Analyzing user: github-actions-web

--- User: github-actions-web (github-service) ---
Account: ************ - AWS-account-development
ARN: arn:aws:iam::************:user/github-actions-web
Created: 2023-09-12T20:39:52+00:00
Inline Policies: 0
Attached Policies: 1
Groups: None
Last Activity: 2023-09-12T20:40:30+00:00

      Analyzing user: github-terraform-serv-acc

--- User: github-terraform-serv-acc (github-service) ---
Account: ************ - AWS-account-development
ARN: arn:aws:iam::************:user/github-terraform-serv-acc
Created: 2023-03-23T16:53:45+00:00
Inline Policies: 0
Attached Policies: 0
Groups: None
Last Activity: 2023-03-23T16:55:07+00:00

      Analyzing user: miroslav.petrovic

--- User: miroslav.petrovic (human-user) ---
Account: ************ - AWS-account-development
ARN: arn:aws:iam::************:user/miroslav.petrovic
Created: 2022-05-06T15:23:58+00:00
Inline Policies: 0
Attached Policies: 1
Groups: None
Last Activity: 2022-05-23T08:12:04+00:00

      Analyzing user: pavel.kaloshin.cli

--- User: pavel.kaloshin.cli (human-user) ---
Account: ************ - AWS-account-development
ARN: arn:aws:iam::************:user/pavel.kaloshin.cli
Created: 2023-11-02T18:50:17+00:00
Inline Policies: 0
Attached Policies: 0
Groups: None
Last Activity: 2023-11-02T18:52:09+00:00

      Analyzing user: slack-notifications

--- User: slack-notifications (other) ---
Account: ************ - AWS-account-development
ARN: arn:aws:iam::************:user/slack-notifications
Created: 2024-04-25T23:15:51+00:00
Inline Policies: 0
Attached Policies: 1
Groups: None
Last Activity: 2024-04-25T23:20:53+00:00

    ✅ Successfully processed 6 users

🔍 Processing Account: ************ (AWS-account-sre-mgmt) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountVPCManagementRole
    External ID: DrataCleanup2025
    ✅ Successfully assumed role and validated credentials
  ✅ Successfully assumed role

📋 Listing IAM users in Account: ************ (AWS-account-sre-mgmt) - Status: ACTIVE
    Found 1 IAM users
      Analyzing user: flhsmv-reports

--- User: flhsmv-reports (other) ---
Account: ************ - AWS-account-sre-mgmt
ARN: arn:aws:iam::************:user/flhsmv-reports
Created: 2023-09-25T23:58:02+00:00
Inline Policies: 1
Attached Policies: 0
Groups: None
Last Activity: 2023-09-25T23:58:02+00:00

    ✅ Successfully processed 1 users

🔍 Processing Account: ************ (Microformas_Citi) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountVPCManagementRole
    External ID: DrataCleanup2025
    ✅ Successfully assumed role and validated credentials
  ✅ Successfully assumed role

📋 Listing IAM users in Account: ************ (Microformas_Citi) - Status: ACTIVE
    Found 5 IAM users
      Analyzing user: Administrator

--- User: Administrator (other) ---
Account: ************ - Microformas_Citi
ARN: arn:aws:iam::************:user/Administrator
Created: 2020-01-21T17:09:21+00:00
Inline Policies: 0
Attached Policies: 1
Groups: None
Last Activity: 2020-01-21T17:09:22+00:00

      Analyzing user: carlos.gutierrez

--- User: carlos.gutierrez (human-user) ---
Account: ************ - Microformas_Citi
ARN: arn:aws:iam::************:user/carlos.gutierrez
Created: 2021-03-05T20:58:48+00:00
Inline Policies: 0
Attached Policies: 1
Groups: None
Last Activity: 2021-03-09T16:46:12+00:00

      Analyzing user: city-encryptedOnboardingFiles-user

--- User: city-encryptedOnboardingFiles-user (other) ---
Account: ************ - Microformas_Citi
ARN: arn:aws:iam::************:user/city-encryptedOnboardingFiles-user
Created: 2021-10-01T18:07:46+00:00
Inline Policies: 0
Attached Policies: 1
Groups: None
Last Activity: 2021-10-01T18:07:47+00:00

      Analyzing user: gerson.garcia

--- User: gerson.garcia (human-user) ---
Account: ************ - Microformas_Citi
ARN: arn:aws:iam::************:user/gerson.garcia
Created: 2020-08-11T01:12:14+00:00
Inline Policies: 0
Attached Policies: 1
Groups: None
Last Activity: No keys

      Analyzing user: opentok

--- User: opentok (other) ---
Account: ************ - Microformas_Citi
ARN: arn:aws:iam::************:user/opentok
Created: 2020-01-28T15:10:06+00:00
Inline Policies: 0
Attached Policies: 0
Groups: None
Last Activity: 2020-01-28T15:10:07+00:00

    ✅ Successfully processed 5 users

🔍 Processing Account: ************ (AWS-account-omni-shared-services) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountVPCManagementRole
    External ID: DrataCleanup2025
    ✅ Successfully assumed role and validated credentials
  ✅ Successfully assumed role

📋 Listing IAM users in Account: ************ (AWS-account-omni-shared-services) - Status: ACTIVE
    Found 2 IAM users
      Analyzing user: github-actions-terraform-account

--- User: github-actions-terraform-account (github-service) ---
Account: ************ - AWS-account-omni-shared-services
ARN: arn:aws:iam::************:user/github-actions-terraform-account
Created: 2024-02-06T16:09:21+00:00
Inline Policies: 0
Attached Policies: 1
Groups: None
Last Activity: 2024-02-06T16:10:04+00:00

      Analyzing user: url-shortener-service-account

--- User: url-shortener-service-account (other) ---
Account: ************ - AWS-account-omni-shared-services
ARN: arn:aws:iam::************:user/url-shortener-service-account
Created: 2024-05-20T17:17:48+00:00
Inline Policies: 0
Attached Policies: 0
Groups: None
Last Activity: 2024-05-20T17:27:17+00:00

    ✅ Successfully processed 2 users

🔍 Processing Account: ************ (aws-account-demo-k8s) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountVPCManagementRole
    External ID: DrataCleanup2025
    ✅ Successfully assumed role and validated credentials
  ✅ Successfully assumed role

📋 Listing IAM users in Account: ************ (aws-account-demo-k8s) - Status: ACTIVE
    Found 1 IAM users
      Analyzing user: github-terraform-serv-acc

--- User: github-terraform-serv-acc (github-service) ---
Account: ************ - aws-account-demo-k8s
ARN: arn:aws:iam::************:user/github-terraform-serv-acc
Created: 2024-07-01T18:33:45+00:00
Inline Policies: 0
Attached Policies: 2
Groups: None
Last Activity: 2024-07-01T18:36:11+00:00

    ✅ Successfully processed 1 users

🔍 Processing Account: ************ (<EMAIL>) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountVPCManagementRole
    External ID: DrataCleanup2025
    ✅ Successfully assumed role and validated credentials
  ✅ Successfully assumed role

📋 Listing IAM users in Account: ************ (<EMAIL>) - Status: ACTIVE
    Found 3 IAM users
      Analyzing user: drp-failover-manager

--- User: drp-failover-manager (drp-service) ---
Account: ************ - <EMAIL>
ARN: arn:aws:iam::************:user/drp-failover-manager
Created: 2025-02-22T21:51:00+00:00
Inline Policies: 0
Attached Policies: 1
Groups: None
Last Activity: 2025-02-22T21:59:53+00:00

      Analyzing user: github-terraform-serv-acc

--- User: github-terraform-serv-acc (github-service) ---
Account: ************ - <EMAIL>
ARN: arn:aws:iam::************:user/github-terraform-serv-acc
Created: 2024-12-09T17:08:18+00:00
Inline Policies: 0
Attached Policies: 2
Groups: None
Last Activity: 2024-12-09T17:09:42+00:00

      Analyzing user: tokbox_vonage_integration

--- User: tokbox_vonage_integration (vonage-service) ---
Account: ************ - <EMAIL>
ARN: arn:aws:iam::************:user/tokbox_vonage_integration
Created: 2025-01-09T00:04:16+00:00
Inline Policies: 0
Attached Policies: 1
Groups: None
Last Activity: 2025-01-09T00:06:11+00:00

    ✅ Successfully processed 3 users

🔍 Processing Account: ************ (aws-account-product-ops-tooling) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountVPCManagementRole
    External ID: DrataCleanup2025
    ✅ Successfully assumed role and validated credentials
  ✅ Successfully assumed role

📋 Listing IAM users in Account: ************ (aws-account-product-ops-tooling) - Status: ACTIVE
    Found 4 IAM users
      Analyzing user: analytics_automations

--- User: analytics_automations (other) ---
Account: ************ - aws-account-product-ops-tooling
ARN: arn:aws:iam::************:user/analytics_automations
Created: 2024-09-09T19:30:54+00:00
Inline Policies: 2
Attached Policies: 1
Groups: None
Last Activity: 2024-09-09T19:31:46+00:00

      Analyzing user: label_studio

--- User: label_studio (other) ---
Account: ************ - aws-account-product-ops-tooling
ARN: arn:aws:iam::************:user/label_studio
Created: 2024-12-17T18:33:11+00:00
Inline Policies: 0
Attached Policies: 1
Groups: None
Last Activity: 2024-12-17T18:35:30+00:00

      Analyzing user: label_studio_admin

--- User: label_studio_admin (other) ---
Account: ************ - aws-account-product-ops-tooling
ARN: arn:aws:iam::************:user/label_studio_admin
Created: 2025-01-07T22:42:39+00:00
Inline Policies: 0
Attached Policies: 1
Groups: None
Last Activity: 2025-01-07T22:45:16+00:00

      Analyzing user: test_s3

--- User: test_s3 (other) ---
Account: ************ - aws-account-product-ops-tooling
ARN: arn:aws:iam::************:user/test_s3
Created: 2024-10-02T23:19:43+00:00
Inline Policies: 0
Attached Policies: 1
Groups: None
Last Activity: 2024-10-02T23:20:48+00:00

    ✅ Successfully processed 4 users

🔍 Processing Account: ************ (AWS-account-filesharing) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountVPCManagementRole
    External ID: DrataCleanup2025
    ✅ Successfully assumed role and validated credentials
  ✅ Successfully assumed role

📋 Listing IAM users in Account: ************ (AWS-account-filesharing) - Status: ACTIVE
    Found 1 IAM users
      Analyzing user: filecloud-server

--- User: filecloud-server (other) ---
Account: ************ - AWS-account-filesharing
ARN: arn:aws:iam::************:user/filecloud-server
Created: 2023-07-02T04:05:03+00:00
Inline Policies: 0
Attached Policies: 1
Groups: None
Last Activity: 2023-07-02T04:13:31+00:00

    ✅ Successfully processed 1 users

🔍 Processing Account: ************ (Metamap Development) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountVPCManagementRole
    External ID: DrataCleanup2025
    ✅ Successfully assumed role and validated credentials
  ✅ Successfully assumed role

📋 Listing IAM users in Account: ************ (Metamap Development) - Status: ACTIVE
    Found 12 IAM users
      Analyzing user: amplify-poc

--- User: amplify-poc (other) ---
Account: ************ - Metamap Development
ARN: arn:aws:iam::************:user/amplify-poc
Created: 2023-11-03T09:05:21+00:00
Inline Policies: 0
Attached Policies: 4
Groups: None
Last Activity: 2023-11-03T09:05:30+00:00

      Analyzing user: chirag_temp

--- User: chirag_temp (other) ---
Account: ************ - Metamap Development
ARN: arn:aws:iam::************:user/chirag_temp
Created: 2024-05-17T20:19:06+00:00
Inline Policies: 0
Attached Policies: 1
Groups: None
Last Activity: 2024-05-17T20:19:17+00:00

      Analyzing user: devel-env-s3-access

--- User: devel-env-s3-access (other) ---
Account: ************ - Metamap Development
ARN: arn:aws:iam::************:user/devel-env-s3-access
Created: 2022-04-27T07:28:55+00:00
Inline Policies: 0
Attached Policies: 1
Groups: None
Last Activity: 2022-04-27T07:28:57+00:00

      Analyzing user: dmitriy.dobrovitsky

--- User: dmitriy.dobrovitsky (human-user) ---
Account: ************ - Metamap Development
ARN: arn:aws:iam::************:user/dmitriy.dobrovitsky
Created: 2025-02-12T14:08:24+00:00
Inline Policies: 0
Attached Policies: 2
Groups: None
Last Activity: No keys

      Analyzing user: localstack-s3-user

--- User: localstack-s3-user (other) ---
Account: ************ - Metamap Development
ARN: arn:aws:iam::************:user/localstack-s3-user
Created: 2023-09-20T09:04:41+00:00
Inline Policies: 1
Attached Policies: 0
Groups: None
Last Activity: 2023-09-20T09:04:51+00:00

      Analyzing user: metamap-security-team

--- User: metamap-security-team (other) ---
Account: ************ - Metamap Development
ARN: arn:aws:iam::************:user/metamap-security-team
Created: 2023-07-25T12:22:31+00:00
Inline Policies: 0
Attached Policies: 0
Groups: None
Last Activity: 2023-11-06T14:15:05+00:00

      Analyzing user: pijain

--- User: pijain (other) ---
Account: ************ - Metamap Development
ARN: arn:aws:iam::************:user/pijain
Created: 2024-08-01T18:39:56+00:00
Inline Policies: 0
Attached Policies: 1
Groups: None
Last Activity: 2024-08-01T18:40:10+00:00

      Analyzing user: serverless-deploy-gitlab

--- User: serverless-deploy-gitlab (other) ---
Account: ************ - Metamap Development
ARN: arn:aws:iam::************:user/serverless-deploy-gitlab
Created: 2022-04-12T07:48:03+00:00
Inline Policies: 1
Attached Policies: 5
Groups: None
Last Activity: 2022-04-12T07:48:04+00:00

      Analyzing user: ses-smtp-user.********-135629

--- User: ses-smtp-user.********-135629 (human-user) ---
Account: ************ - Metamap Development
ARN: arn:aws:iam::************:user/ses-smtp-user.********-135629
Created: 2023-02-06T08:26:33+00:00
Inline Policies: 1
Attached Policies: 0
Groups: None
Last Activity: 2023-02-06T08:26:33+00:00

      Analyzing user: ses_devel

--- User: ses_devel (other) ---
Account: ************ - Metamap Development
ARN: arn:aws:iam::************:user/ses_devel
Created: 2021-08-25T08:17:09+00:00
Inline Policies: 0
Attached Policies: 0
Groups: None
Last Activity: 2021-08-25T08:17:09+00:00

      Analyzing user: video-consent-test-user

--- User: video-consent-test-user (other) ---
Account: ************ - Metamap Development
ARN: arn:aws:iam::************:user/video-consent-test-user
Created: 2022-10-04T08:08:05+00:00
Inline Policies: 0
Attached Policies: 1
Groups: None
Last Activity: 2022-10-04T08:08:06+00:00

      Analyzing user: vision-model-mesh

--- User: vision-model-mesh (other) ---
Account: ************ - Metamap Development
ARN: arn:aws:iam::************:user/vision-model-mesh
Created: 2024-01-22T13:07:27+00:00
Inline Policies: 0
Attached Policies: 1
Groups: None
Last Activity: 2024-01-22T13:07:49+00:00

    ✅ Successfully processed 12 users

🔍 Processing Account: ************ (<EMAIL>) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountVPCManagementRole
    External ID: DrataCleanup2025
    ✅ Successfully assumed role and validated credentials
  ✅ Successfully assumed role

📋 Listing IAM users in Account: ************ (<EMAIL>) - Status: ACTIVE
    Found 0 IAM users
    No IAM users found in this account

🔍 Processing Account: ************ (<EMAIL>) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountVPCManagementRole
    External ID: DrataCleanup2025
    ✅ Successfully assumed role and validated credentials
  ✅ Successfully assumed role

📋 Listing IAM users in Account: ************ (<EMAIL>) - Status: ACTIVE
    Found 0 IAM users
    No IAM users found in this account

🔍 Processing Account: ************ (<EMAIL>) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountVPCManagementRole
    External ID: DrataCleanup2025
    ✅ Successfully assumed role and validated credentials
  ✅ Successfully assumed role

📋 Listing IAM users in Account: ************ (<EMAIL>) - Status: ACTIVE
    Found 0 IAM users
    No IAM users found in this account

🔍 Processing Account: ************ (<EMAIL>) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountVPCManagementRole
    External ID: DrataCleanup2025
    ✅ Successfully assumed role and validated credentials
  ✅ Successfully assumed role

📋 Listing IAM users in Account: ************ (<EMAIL>) - Status: ACTIVE
    Found 0 IAM users
    No IAM users found in this account

🔍 Processing Account: ************ (<EMAIL>) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountVPCManagementRole
    External ID: DrataCleanup2025
    ✅ Successfully assumed role and validated credentials
  ✅ Successfully assumed role

📋 Listing IAM users in Account: ************ (<EMAIL>) - Status: ACTIVE
    Found 2 IAM users
      Analyzing user: keycloak-77h3o78b-sa

--- User: keycloak-77h3o78b-sa (keycloak-service) ---
Account: ************ - <EMAIL>
ARN: arn:aws:iam::************:user/keycloak-77h3o78b-sa
Created: 2024-07-18T15:35:35+00:00
Inline Policies: 0
Attached Policies: 1
Groups: None
Last Activity: 2024-07-18T15:35:41+00:00

      Analyzing user: keycloak-ygp3jid3-sa

--- User: keycloak-ygp3jid3-sa (keycloak-service) ---
Account: ************ - <EMAIL>
ARN: arn:aws:iam::************:user/keycloak-ygp3jid3-sa
Created: 2024-11-17T19:28:35+00:00
Inline Policies: 0
Attached Policies: 1
Groups: None
Last Activity: 2024-11-17T19:28:44+00:00

    ✅ Successfully processed 2 users

🔍 Processing Account: ************ (Metamap Data) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountVPCManagementRole
    External ID: DrataCleanup2025
    ✅ Successfully assumed role and validated credentials
  ✅ Successfully assumed role

📋 Listing IAM users in Account: ************ (Metamap Data) - Status: ACTIVE
    Found 1 IAM users
      Analyzing user: mmp-s3-user

--- User: mmp-s3-user (other) ---
Account: ************ - Metamap Data
ARN: arn:aws:iam::************:user/mmp-s3-user
Created: 2024-02-06T04:56:06+00:00
Inline Policies: 2
Attached Policies: 6
Groups: None
Last Activity: 2024-04-16T05:31:10+00:00

    ✅ Successfully processed 1 users

🔍 Processing Account: ************ (AWS-account-FL-HSVM-pilot) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountVPCManagementRole
    External ID: DrataCleanup2025
    ✅ Successfully assumed role and validated credentials
  ✅ Successfully assumed role

📋 Listing IAM users in Account: ************ (AWS-account-FL-HSVM-pilot) - Status: ACTIVE
    Found 2 IAM users
      Analyzing user: github-terraform-serv-acc

--- User: github-terraform-serv-acc (github-service) ---
Account: ************ - AWS-account-FL-HSVM-pilot
ARN: arn:aws:iam::************:user/github-terraform-serv-acc
Created: 2023-05-24T21:27:23+00:00
Inline Policies: 0
Attached Policies: 2
Groups: None
Last Activity: 2023-05-24T21:46:23+00:00

      Analyzing user: hsmv_gateway_user

--- User: hsmv_gateway_user (other) ---
Account: ************ - AWS-account-FL-HSVM-pilot
ARN: arn:aws:iam::************:user/hsmv_gateway_user
Created: 2023-06-16T13:46:28+00:00
Inline Policies: 0
Attached Policies: 1
Groups: None
Last Activity: 2023-06-16T13:47:40+00:00

    ✅ Successfully processed 2 users

🔍 Processing Account: ************ (Athena Production) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountVPCManagementRole
    External ID: DrataCleanup2025
    ✅ Successfully assumed role and validated credentials
  ✅ Successfully assumed role

📋 Listing IAM users in Account: ************ (Athena Production) - Status: ACTIVE
    Found 3 IAM users
      Analyzing user: athena-prod-s3-user

--- User: athena-prod-s3-user (other) ---
Account: ************ - Athena Production
ARN: arn:aws:iam::************:user/athena-prod-s3-user
Created: 2021-11-17T10:09:14+00:00
Inline Policies: 0
Attached Policies: 1
Groups: None
Last Activity: 2021-11-17T10:09:15+00:00

      Analyzing user: athenascrapingengine-prod-s3

--- User: athenascrapingengine-prod-s3 (other) ---
Account: ************ - Athena Production
ARN: arn:aws:iam::************:user/athenascrapingengine-prod-s3
Created: 2021-11-17T10:10:47+00:00
Inline Policies: 0
Attached Policies: 1
Groups: None
Last Activity: 2021-11-17T10:10:48+00:00

      Analyzing user: sergio.acosta

--- User: sergio.acosta (human-user) ---
Account: ************ - Athena Production
ARN: arn:aws:iam::************:user/sergio.acosta
Created: 2024-09-10T21:18:14+00:00
Inline Policies: 0
Attached Policies: 0
Groups: None
Last Activity: No keys

    ✅ Successfully processed 3 users

🔍 Processing Account: ************ (ct-log-audit) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountVPCManagementRole
    External ID: DrataCleanup2025
    ✅ Successfully assumed role and validated credentials
  ✅ Successfully assumed role

📋 Listing IAM users in Account: ************ (ct-log-audit) - Status: ACTIVE
    Found 0 IAM users
    No IAM users found in this account

🔍 Processing Account: ************ (<EMAIL>) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountVPCManagementRole
    External ID: DrataCleanup2025
    ✅ Successfully assumed role and validated credentials
  ✅ Successfully assumed role

📋 Listing IAM users in Account: ************ (<EMAIL>) - Status: ACTIVE
    Found 3 IAM users
      Analyzing user: github-terraform-serv-acc

--- User: github-terraform-serv-acc (github-service) ---
Account: ************ - <EMAIL>
ARN: arn:aws:iam::************:user/github-terraform-serv-acc
Created: 2024-11-05T00:03:16+00:00
Inline Policies: 0
Attached Policies: 2
Groups: None
Last Activity: 2024-11-05T00:04:24+00:00

      Analyzing user: keycloak-4bqtydgz-sa

--- User: keycloak-4bqtydgz-sa (keycloak-service) ---
Account: ************ - <EMAIL>
ARN: arn:aws:iam::************:user/keycloak-4bqtydgz-sa
Created: 2024-11-07T08:47:33+00:00
Inline Policies: 0
Attached Policies: 1
Groups: None
Last Activity: 2024-11-07T08:49:33+00:00

      Analyzing user: tokbox_vonage_integration

--- User: tokbox_vonage_integration (vonage-service) ---
Account: ************ - <EMAIL>
ARN: arn:aws:iam::************:user/tokbox_vonage_integration
Created: 2024-11-06T05:48:39+00:00
Inline Policies: 0
Attached Policies: 1
Groups: None
Last Activity: 2024-11-06T05:49:01+00:00

    ✅ Successfully processed 3 users

🔍 Processing Account: ************ (aws-account-citimx-dev) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountVPCManagementRole
    External ID: DrataCleanup2025
    ✅ Successfully assumed role and validated credentials
  ✅ Successfully assumed role

📋 Listing IAM users in Account: ************ (aws-account-citimx-dev) - Status: ACTIVE
    Found 3 IAM users
      Analyzing user: github-terraform-serv-acc

--- User: github-terraform-serv-acc (github-service) ---
Account: ************ - aws-account-citimx-dev
ARN: arn:aws:iam::************:user/github-terraform-serv-acc
Created: 2024-05-06T23:36:20+00:00
Inline Policies: 0
Attached Policies: 2
Groups: None
Last Activity: 2024-05-06T23:39:34+00:00

      Analyzing user: keycloak-d9pf2nbz-sa

--- User: keycloak-d9pf2nbz-sa (keycloak-service) ---
Account: ************ - aws-account-citimx-dev
ARN: arn:aws:iam::************:user/keycloak-d9pf2nbz-sa
Created: 2024-05-07T08:53:26+00:00
Inline Policies: 0
Attached Policies: 1
Groups: None
Last Activity: 2024-05-07T08:53:34+00:00

      Analyzing user: tokbox_vonage_integrationCitimxdev

--- User: tokbox_vonage_integrationCitimxdev (vonage-service) ---
Account: ************ - aws-account-citimx-dev
ARN: arn:aws:iam::************:user/tokbox_vonage_integrationCitimxdev
Created: 2024-05-08T00:18:45+00:00
Inline Policies: 0
Attached Policies: 1
Groups: None
Last Activity: 2024-05-08T00:19:50+00:00

    ✅ Successfully processed 3 users

🔍 Processing Account: ************ (Metamap AI) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountVPCManagementRole
    External ID: DrataCleanup2025
    ✅ Successfully assumed role and validated credentials
  ✅ Successfully assumed role

📋 Listing IAM users in Account: ************ (Metamap AI) - Status: ACTIVE
    Found 1 IAM users
      Analyzing user: rapyder

--- User: rapyder (other) ---
Account: ************ - Metamap AI
ARN: arn:aws:iam::************:user/rapyder
Created: 2024-02-23T16:19:10+00:00
Inline Policies: 0
Attached Policies: 2
Groups: None
Last Activity: No keys

    ✅ Successfully processed 1 users

🔍 Processing Account: ************ (ct-log-archive) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountVPCManagementRole
    External ID: DrataCleanup2025
    ✅ Successfully assumed role and validated credentials
  ✅ Successfully assumed role

📋 Listing IAM users in Account: ************ (ct-log-archive) - Status: ACTIVE
    Found 0 IAM users
    No IAM users found in this account

🔍 Processing Account: ************ (Metamap Production) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountVPCManagementRole
    External ID: DrataCleanup2025
    ✅ Successfully assumed role and validated credentials
  ✅ Successfully assumed role

📋 Listing IAM users in Account: ************ (Metamap Production) - Status: ACTIVE
    Found 15 IAM users
      Analyzing user: att-cron

--- User: att-cron (other) ---
Account: ************ - Metamap Production
ARN: arn:aws:iam::************:user/att-cron
Created: 2024-03-28T07:08:33+00:00
Inline Policies: 0
Attached Policies: 1
Groups: None
Last Activity: 2024-03-28T07:08:59+00:00

      Analyzing user: dmitriy.dobrovitsky

--- User: dmitriy.dobrovitsky (human-user) ---
Account: ************ - Metamap Production
ARN: arn:aws:iam::************:user/dmitriy.dobrovitsky
Created: 2025-01-23T09:16:20+00:00
Inline Policies: 2
Attached Policies: 2
Groups: None
Last Activity: No keys

      Analyzing user: dmitry

--- User: dmitry (other) ---
Account: ************ - Metamap Production
ARN: arn:aws:iam::************:user/dmitry
Created: 2024-11-08T12:09:33+00:00
Inline Policies: 1
Attached Policies: 2
Groups: None
Last Activity: No keys

      Analyzing user: gitlab-ci-prod

--- User: gitlab-ci-prod (other) ---
Account: ************ - Metamap Production
ARN: arn:aws:iam::************:user/gitlab-ci-prod
Created: 2023-11-09T07:09:11+00:00
Inline Policies: 0
Attached Policies: 1
Groups: None
Last Activity: 2023-11-09T07:09:26+00:00

      Analyzing user: gitlab-ses

--- User: gitlab-ses (other) ---
Account: ************ - Metamap Production
ARN: arn:aws:iam::************:user/gitlab-ses
Created: 2024-05-22T18:49:51+00:00
Inline Policies: 1
Attached Policies: 0
Groups: None
Last Activity: 2024-05-22T18:49:51+00:00

      Analyzing user: incode-team

--- User: incode-team (other) ---
Account: ************ - Metamap Production
ARN: arn:aws:iam::************:user/incode-team
Created: 2024-07-31T06:19:11+00:00
Inline Policies: 0
Attached Policies: 1
Groups: None
Last Activity: 2024-07-31T06:19:26+00:00

      Analyzing user: metamap-data-sync

--- User: metamap-data-sync (other) ---
Account: ************ - Metamap Production
ARN: arn:aws:iam::************:user/metamap-data-sync
Created: 2023-12-12T14:10:19+00:00
Inline Policies: 0
Attached Policies: 1
Groups: None
Last Activity: 2024-01-09T08:13:25+00:00

      Analyzing user: metamap-security-team

--- User: metamap-security-team (other) ---
Account: ************ - Metamap Production
ARN: arn:aws:iam::************:user/metamap-security-team
Created: 2023-07-25T12:25:12+00:00
Inline Policies: 0
Attached Policies: 0
Groups: None
Last Activity: 2023-11-06T14:13:06+00:00

      Analyzing user: pmm-rds

--- User: pmm-rds (other) ---
Account: ************ - Metamap Production
ARN: arn:aws:iam::************:user/pmm-rds
Created: 2024-07-20T05:22:48+00:00
Inline Policies: 0
Attached Policies: 1
Groups: None
Last Activity: 2024-07-20T05:23:03+00:00

      Analyzing user: QAB_TOOL

--- User: QAB_TOOL (other) ---
Account: ************ - Metamap Production
ARN: arn:aws:iam::************:user/QAB_TOOL
Created: 2024-03-26T08:18:09+00:00
Inline Policies: 0
Attached Policies: 1
Groups: None
Last Activity: 2024-03-26T08:18:21+00:00

      Analyzing user: shashi.shekhar

--- User: shashi.shekhar (human-user) ---
Account: ************ - Metamap Production
ARN: arn:aws:iam::************:user/shashi.shekhar
Created: 2025-03-20T18:09:28+00:00
Inline Policies: 3
Attached Policies: 3
Groups: None
Last Activity: No keys

      Analyzing user: stats-s3

--- User: stats-s3 (other) ---
Account: ************ - Metamap Production
ARN: arn:aws:iam::************:user/stats-s3
Created: 2024-03-13T11:01:50+00:00
Inline Policies: 0
Attached Policies: 1
Groups: None
Last Activity: 2024-03-13T11:02:05+00:00

      Analyzing user: temp-data-deletion

--- User: temp-data-deletion (other) ---
Account: ************ - Metamap Production
ARN: arn:aws:iam::************:user/temp-data-deletion
Created: 2024-05-20T17:58:58+00:00
Inline Policies: 0
Attached Policies: 1
Groups: None
Last Activity: 2024-05-20T17:59:11+00:00

      Analyzing user: vision-triton-server-prod

--- User: vision-triton-server-prod (other) ---
Account: ************ - Metamap Production
ARN: arn:aws:iam::************:user/vision-triton-server-prod
Created: 2023-06-16T08:02:17+00:00
Inline Policies: 0
Attached Policies: 1
Groups: None
Last Activity: 2023-06-16T08:02:56+00:00

      Analyzing user: VISION_TRAINING

--- User: VISION_TRAINING (other) ---
Account: ************ - Metamap Production
ARN: arn:aws:iam::************:user/VISION_TRAINING
Created: 2024-03-26T17:02:00+00:00
Inline Policies: 0
Attached Policies: 1
Groups: None
Last Activity: 2024-03-26T17:02:11+00:00

    ✅ Successfully processed 15 users

🔍 Processing Account: ************ (AWS-account-CA-DMV-pilot) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountVPCManagementRole
    External ID: DrataCleanup2025
    ✅ Successfully assumed role and validated credentials
  ✅ Successfully assumed role

📋 Listing IAM users in Account: ************ (AWS-account-CA-DMV-pilot) - Status: ACTIVE
    Found 0 IAM users
    No IAM users found in this account

🔍 Processing Account: ************ (CitiBanamex Analytics Account) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountVPCManagementRole
    External ID: DrataCleanup2025
    ✅ Successfully assumed role and validated credentials
  ✅ Successfully assumed role

📋 Listing IAM users in Account: ************ (CitiBanamex Analytics Account) - Status: ACTIVE
    Found 0 IAM users
    No IAM users found in this account

🔍 Processing Account: ************ (AWS-account-demo) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountVPCManagementRole
    External ID: DrataCleanup2025
    ✅ Successfully assumed role and validated credentials
  ✅ Successfully assumed role

📋 Listing IAM users in Account: ************ (AWS-account-demo) - Status: ACTIVE
    Found 5 IAM users
      Analyzing user: github-actions-web

--- User: github-actions-web (github-service) ---
Account: ************ - AWS-account-demo
ARN: arn:aws:iam::************:user/github-actions-web
Created: 2023-07-12T02:27:29+00:00
Inline Policies: 0
Attached Policies: 1
Groups: None
Last Activity: 2023-07-12T02:28:41+00:00

      Analyzing user: github-terraform-serv-acc

--- User: github-terraform-serv-acc (github-service) ---
Account: ************ - AWS-account-demo
ARN: arn:aws:iam::************:user/github-terraform-serv-acc
Created: 2023-03-16T19:33:28+00:00
Inline Policies: 0
Attached Policies: 0
Groups: None
Last Activity: 2023-03-16T19:34:58+00:00

      Analyzing user: IncodeUserServiceSydney

--- User: IncodeUserServiceSydney (other) ---
Account: ************ - AWS-account-demo
ARN: arn:aws:iam::************:user/IncodeUserServiceSydney
Created: 2023-05-03T18:00:12+00:00
Inline Policies: 0
Attached Policies: 5
Groups: None
Last Activity: 2023-05-03T18:01:12+00:00

      Analyzing user: tokbox_vonage_integration_jakarta

--- User: tokbox_vonage_integration_jakarta (vonage-service) ---
Account: ************ - AWS-account-demo
ARN: arn:aws:iam::************:user/tokbox_vonage_integration_jakarta
Created: 2023-05-25T20:24:14+00:00
Inline Policies: 0
Attached Policies: 1
Groups: None
Last Activity: 2023-05-25T20:25:15+00:00

      Analyzing user: tokbox_vonage_integration_sydney

--- User: tokbox_vonage_integration_sydney (vonage-service) ---
Account: ************ - AWS-account-demo
ARN: arn:aws:iam::************:user/tokbox_vonage_integration_sydney
Created: 2023-05-26T01:42:30+00:00
Inline Policies: 0
Attached Policies: 1
Groups: None
Last Activity: 2023-05-26T01:43:35+00:00

    ✅ Successfully processed 5 users

🔍 Processing Account: ************ (Athena Devel) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountVPCManagementRole
    External ID: DrataCleanup2025
    ✅ Successfully assumed role and validated credentials
  ✅ Successfully assumed role

📋 Listing IAM users in Account: ************ (Athena Devel) - Status: ACTIVE
    Found 5 IAM users
      Analyzing user: athenadev-s3-user

--- User: athenadev-s3-user (other) ---
Account: ************ - Athena Devel
ARN: arn:aws:iam::************:user/athenadev-s3-user
Created: 2021-09-06T05:26:50+00:00
Inline Policies: 0
Attached Policies: 1
Groups: None
Last Activity: 2021-09-06T05:26:52+00:00

      Analyzing user: athenascrapingengine-staging-s3-user

--- User: athenascrapingengine-staging-s3-user (other) ---
Account: ************ - Athena Devel
ARN: arn:aws:iam::************:user/athenascrapingengine-staging-s3-user
Created: 2021-11-12T06:19:58+00:00
Inline Policies: 0
Attached Policies: 1
Groups: None
Last Activity: 2021-11-12T06:19:59+00:00

      Analyzing user: athenascrapingenginedev-s3-user

--- User: athenascrapingenginedev-s3-user (other) ---
Account: ************ - Athena Devel
ARN: arn:aws:iam::************:user/athenascrapingenginedev-s3-user
Created: 2021-09-21T05:53:30+00:00
Inline Policies: 0
Attached Policies: 1
Groups: None
Last Activity: 2021-09-21T05:53:32+00:00

      Analyzing user: athenaservice-staging-s3-user

--- User: athenaservice-staging-s3-user (other) ---
Account: ************ - Athena Devel
ARN: arn:aws:iam::************:user/athenaservice-staging-s3-user
Created: 2021-11-12T06:18:30+00:00
Inline Policies: 0
Attached Policies: 1
Groups: None
Last Activity: 2021-11-12T06:18:31+00:00

      Analyzing user: metamap-security-team

--- User: metamap-security-team (other) ---
Account: ************ - Athena Devel
ARN: arn:aws:iam::************:user/metamap-security-team
Created: 2023-07-25T11:21:34+00:00
Inline Policies: 0
Attached Policies: 0
Groups: None
Last Activity: 2023-11-06T14:17:29+00:00

    ✅ Successfully processed 5 users

🔍 Processing Account: ************ (AWS-account-pollid) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountVPCManagementRole
    External ID: DrataCleanup2025
    ✅ Successfully assumed role and validated credentials
  ✅ Successfully assumed role

📋 Listing IAM users in Account: ************ (AWS-account-pollid) - Status: ACTIVE
    Found 0 IAM users
    No IAM users found in this account

🔍 Processing Account: ************ (AWS-account-georgia-dds-stage) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountVPCManagementRole
    External ID: DrataCleanup2025
    ✅ Successfully assumed role and validated credentials
  ✅ Successfully assumed role

📋 Listing IAM users in Account: ************ (AWS-account-georgia-dds-stage) - Status: ACTIVE
    Found 0 IAM users
    No IAM users found in this account

🔍 Processing Account: ************ (aws-account-citimx-prod) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountVPCManagementRole
    External ID: DrataCleanup2025
    ✅ Successfully assumed role and validated credentials
  ✅ Successfully assumed role

📋 Listing IAM users in Account: ************ (aws-account-citimx-prod) - Status: ACTIVE
    Found 3 IAM users
      Analyzing user: github-terraform-serv-acc

--- User: github-terraform-serv-acc (github-service) ---
Account: ************ - aws-account-citimx-prod
ARN: arn:aws:iam::************:user/github-terraform-serv-acc
Created: 2024-10-03T16:40:48+00:00
Inline Policies: 0
Attached Policies: 2
Groups: None
Last Activity: 2024-10-03T20:00:23+00:00

      Analyzing user: keycloak-nxjs2rt2-sa

--- User: keycloak-nxjs2rt2-sa (keycloak-service) ---
Account: ************ - aws-account-citimx-prod
ARN: arn:aws:iam::************:user/keycloak-nxjs2rt2-sa
Created: 2024-10-04T02:58:55+00:00
Inline Policies: 0
Attached Policies: 1
Groups: None
Last Activity: 2024-10-04T02:59:00+00:00

      Analyzing user: tokbox_vonage_integrationCitimxprod

--- User: tokbox_vonage_integrationCitimxprod (vonage-service) ---
Account: ************ - aws-account-citimx-prod
ARN: arn:aws:iam::************:user/tokbox_vonage_integrationCitimxprod
Created: 2024-10-03T16:42:34+00:00
Inline Policies: 0
Attached Policies: 1
Groups: None
Last Activity: 2024-10-07T06:11:59+00:00

    ✅ Successfully processed 3 users

🔍 Processing Account: ************ (<EMAIL>) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountVPCManagementRole
    External ID: DrataCleanup2025
    ✅ Successfully assumed role and validated credentials
  ✅ Successfully assumed role

📋 Listing IAM users in Account: ************ (<EMAIL>) - Status: ACTIVE
    Found 0 IAM users
    No IAM users found in this account

🔍 Processing Account: ************ (aws-account-canada-k8s) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountVPCManagementRole
    External ID: DrataCleanup2025
    ✅ Successfully assumed role and validated credentials
  ✅ Successfully assumed role

📋 Listing IAM users in Account: ************ (aws-account-canada-k8s) - Status: ACTIVE
    Found 3 IAM users
      Analyzing user: github-terraform-serv-acc

--- User: github-terraform-serv-acc (github-service) ---
Account: ************ - aws-account-canada-k8s
ARN: arn:aws:iam::************:user/github-terraform-serv-acc
Created: 2025-05-06T18:51:26+00:00
Inline Policies: 0
Attached Policies: 2
Groups: None
Last Activity: No keys

      Analyzing user: keycloak-2ibazywi-sa

--- User: keycloak-2ibazywi-sa (keycloak-service) ---
Account: ************ - aws-account-canada-k8s
ARN: arn:aws:iam::************:user/keycloak-2ibazywi-sa
Created: 2025-05-07T01:27:50+00:00
Inline Policies: 0
Attached Policies: 1
Groups: None
Last Activity: 2025-05-07T01:27:55+00:00

      Analyzing user: tokbox_vonage_integrationCanadaSAAS

--- User: tokbox_vonage_integrationCanadaSAAS (vonage-service) ---
Account: ************ - aws-account-canada-k8s
ARN: arn:aws:iam::************:user/tokbox_vonage_integrationCanadaSAAS
Created: 2025-05-07T01:16:16+00:00
Inline Policies: 0
Attached Policies: 1
Groups: None
Last Activity: 2025-05-07T01:17:04+00:00

    ✅ Successfully processed 3 users

🔍 Processing Account: ************ (AWS-account-saas-australia) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountVPCManagementRole
    External ID: DrataCleanup2025
    ✅ Successfully assumed role and validated credentials
  ✅ Successfully assumed role

📋 Listing IAM users in Account: ************ (AWS-account-saas-australia) - Status: ACTIVE
    Found 3 IAM users
      Analyzing user: github-actions-web

--- User: github-actions-web (github-service) ---
Account: ************ - AWS-account-saas-australia
ARN: arn:aws:iam::************:user/github-actions-web
Created: 2023-11-07T23:08:44+00:00
Inline Policies: 0
Attached Policies: 1
Groups: None
Last Activity: 2023-11-07T23:09:53+00:00

      Analyzing user: ServiceUserAccount

--- User: ServiceUserAccount (other) ---
Account: ************ - AWS-account-saas-australia
ARN: arn:aws:iam::************:user/ServiceUserAccount
Created: 2023-06-02T22:36:26+00:00
Inline Policies: 0
Attached Policies: 0
Groups: None
Last Activity: 2023-06-02T22:38:25+00:00

      Analyzing user: tokbox_vonage_integration_sydney

--- User: tokbox_vonage_integration_sydney (vonage-service) ---
Account: ************ - AWS-account-saas-australia
ARN: arn:aws:iam::************:user/tokbox_vonage_integration_sydney
Created: 2023-06-16T23:01:02+00:00
Inline Policies: 0
Attached Policies: 1
Groups: None
Last Activity: 2023-06-16T23:01:41+00:00

    ✅ Successfully processed 3 users

🔍 Processing Account: ************ (AWS Account Colombia SOR) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountVPCManagementRole
    External ID: DrataCleanup2025
    ✅ Successfully assumed role and validated credentials
  ✅ Successfully assumed role

📋 Listing IAM users in Account: ************ (AWS Account Colombia SOR) - Status: ACTIVE
    Found 1 IAM users
      Analyzing user: btc-co-user

--- User: btc-co-user (other) ---
Account: ************ - AWS Account Colombia SOR
ARN: arn:aws:iam::************:user/btc-co-user
Created: 2023-11-02T06:53:34+00:00
Inline Policies: 0
Attached Policies: 1
Groups: None
Last Activity: 2023-11-02T06:54:08+00:00

    ✅ Successfully processed 1 users

🔍 Processing Account: ************ (aws-account-ml) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountVPCManagementRole
    External ID: DrataCleanup2025
    ✅ Successfully assumed role and validated credentials
  ✅ Successfully assumed role

📋 Listing IAM users in Account: ************ (aws-account-ml) - Status: ACTIVE
    Found 35 IAM users
      Analyzing user: clearml-aws-autoscaler

--- User: clearml-aws-autoscaler (ml-service) ---
Account: ************ - aws-account-ml
ARN: arn:aws:iam::************:user/clearml-aws-autoscaler
Created: 2022-07-07T16:50:14+00:00
Inline Policies: 0
Attached Policies: 0
Groups: None
Last Activity: 2022-07-07T16:50:15+00:00

      Analyzing user: clearml-ci-s3-read

--- User: clearml-ci-s3-read (ml-service) ---
Account: ************ - aws-account-ml
ARN: arn:aws:iam::************:user/clearml-ci-s3-read
Created: 2023-08-28T13:33:38+00:00
Inline Policies: 0
Attached Policies: 2
Groups: None
Last Activity: 2023-08-28T13:34:18+00:00

      Analyzing user: clearml-monitoring-cloudwatch

--- User: clearml-monitoring-cloudwatch (ml-service) ---
Account: ************ - aws-account-ml
ARN: arn:aws:iam::************:user/clearml-monitoring-cloudwatch
Created: 2023-09-07T15:51:45+00:00
Inline Policies: 0
Attached Policies: 0
Groups: None
Last Activity: 2023-09-07T15:52:42+00:00

      Analyzing user: clearml-s3-access

--- User: clearml-s3-access (ml-service) ---
Account: ************ - aws-account-ml
ARN: arn:aws:iam::************:user/clearml-s3-access
Created: 2022-07-05T08:23:50+00:00
Inline Policies: 1
Attached Policies: 0
Groups: None
Last Activity: 2025-05-27T14:03:54+00:00

      Analyzing user: clearml-support

--- User: clearml-support (ml-service) ---
Account: ************ - aws-account-ml
ARN: arn:aws:iam::************:user/clearml-support
Created: 2022-05-23T08:35:20+00:00
Inline Policies: 1
Attached Policies: 1
Groups: None
Last Activity: 2022-05-31T09:27:44+00:00

      Analyzing user: data-access.aleksei.grishin

--- User: data-access.aleksei.grishin (human-user) ---
Account: ************ - aws-account-ml
ARN: arn:aws:iam::************:user/data-access.aleksei.grishin
Created: 2025-03-04T15:26:44+00:00
Inline Policies: 0
Attached Policies: 0
Groups: None
Last Activity: 2025-03-21T14:23:50+00:00

      Analyzing user: data-access.alex.golunov

--- User: data-access.alex.golunov (human-user) ---
Account: ************ - aws-account-ml
ARN: arn:aws:iam::************:user/data-access.alex.golunov
Created: 2025-03-03T09:49:10+00:00
Inline Policies: 0
Attached Policies: 0
Groups: None
Last Activity: 2025-03-03T09:52:30+00:00

      Analyzing user: data-access.alexander.andrusenko

--- User: data-access.alexander.andrusenko (human-user) ---
Account: ************ - aws-account-ml
ARN: arn:aws:iam::************:user/data-access.alexander.andrusenko
Created: 2025-03-04T14:48:59+00:00
Inline Policies: 0
Attached Policies: 0
Groups: None
Last Activity: 2025-03-11T14:27:18+00:00

      Analyzing user: data-access.alexander.komarov

--- User: data-access.alexander.komarov (human-user) ---
Account: ************ - aws-account-ml
ARN: arn:aws:iam::************:user/data-access.alexander.komarov
Created: 2025-03-04T15:20:00+00:00
Inline Policies: 0
Attached Policies: 0
Groups: None
Last Activity: 2025-03-05T09:31:03+00:00

      Analyzing user: data-access.alexander.lekomtsev

--- User: data-access.alexander.lekomtsev (human-user) ---
Account: ************ - aws-account-ml
ARN: arn:aws:iam::************:user/data-access.alexander.lekomtsev
Created: 2025-03-04T15:56:01+00:00
Inline Policies: 0
Attached Policies: 0
Groups: None
Last Activity: 2025-03-10T10:55:45+00:00

      Analyzing user: data-access.alexey.tochin

--- User: data-access.alexey.tochin (human-user) ---
Account: ************ - aws-account-ml
ARN: arn:aws:iam::************:user/data-access.alexey.tochin
Created: 2025-03-04T14:47:09+00:00
Inline Policies: 0
Attached Policies: 0
Groups: None
Last Activity: 2025-03-05T15:27:44+00:00

      Analyzing user: data-access.anastasiia.evteeva

--- User: data-access.anastasiia.evteeva (human-user) ---
Account: ************ - aws-account-ml
ARN: arn:aws:iam::************:user/data-access.anastasiia.evteeva
Created: 2025-03-04T15:29:22+00:00
Inline Policies: 0
Attached Policies: 0
Groups: None
Last Activity: 2025-03-10T10:52:03+00:00

      Analyzing user: data-access.darko.tica

--- User: data-access.darko.tica (human-user) ---
Account: ************ - aws-account-ml
ARN: arn:aws:iam::************:user/data-access.darko.tica
Created: 2025-03-04T15:44:43+00:00
Inline Policies: 0
Attached Policies: 0
Groups: None
Last Activity: 2025-03-10T12:25:50+00:00

      Analyzing user: data-access.dusko.petrovic

--- User: data-access.dusko.petrovic (human-user) ---
Account: ************ - aws-account-ml
ARN: arn:aws:iam::************:user/data-access.dusko.petrovic
Created: 2025-03-04T15:27:49+00:00
Inline Policies: 0
Attached Policies: 0
Groups: None
Last Activity: 2025-03-10T15:03:09+00:00

      Analyzing user: data-access.efim.boieru

--- User: data-access.efim.boieru (human-user) ---
Account: ************ - aws-account-ml
ARN: arn:aws:iam::************:user/data-access.efim.boieru
Created: 2025-03-04T15:27:14+00:00
Inline Policies: 0
Attached Policies: 0
Groups: None
Last Activity: 2025-03-18T11:09:14+00:00

      Analyzing user: data-access.irena.djordjevic

--- User: data-access.irena.djordjevic (human-user) ---
Account: ************ - aws-account-ml
ARN: arn:aws:iam::************:user/data-access.irena.djordjevic
Created: 2025-03-04T15:53:41+00:00
Inline Policies: 0
Attached Policies: 0
Groups: None
Last Activity: 2025-03-11T11:05:49+00:00

      Analyzing user: data-access.ivan.gluvacevic

--- User: data-access.ivan.gluvacevic (human-user) ---
Account: ************ - aws-account-ml
ARN: arn:aws:iam::************:user/data-access.ivan.gluvacevic
Created: 2025-03-03T09:08:40+00:00
Inline Policies: 0
Attached Policies: 0
Groups: None
Last Activity: 2025-03-03T14:16:14+00:00

      Analyzing user: data-access.ivan.kobets

--- User: data-access.ivan.kobets (human-user) ---
Account: ************ - aws-account-ml
ARN: arn:aws:iam::************:user/data-access.ivan.kobets
Created: 2025-03-04T15:55:03+00:00
Inline Policies: 0
Attached Policies: 0
Groups: None
Last Activity: 2025-03-04T16:38:34+00:00

      Analyzing user: data-access.kirill.kozlov

--- User: data-access.kirill.kozlov (human-user) ---
Account: ************ - aws-account-ml
ARN: arn:aws:iam::************:user/data-access.kirill.kozlov
Created: 2025-03-04T15:43:50+00:00
Inline Policies: 0
Attached Policies: 0
Groups: None
Last Activity: No keys

      Analyzing user: data-access.konstantinos.peppas

--- User: data-access.konstantinos.peppas (human-user) ---
Account: ************ - aws-account-ml
ARN: arn:aws:iam::************:user/data-access.konstantinos.peppas
Created: 2025-03-04T15:18:34+00:00
Inline Policies: 0
Attached Policies: 0
Groups: None
Last Activity: 2025-03-07T10:42:24+00:00

      Analyzing user: data-access.lazar.lazarevic

--- User: data-access.lazar.lazarevic (human-user) ---
Account: ************ - aws-account-ml
ARN: arn:aws:iam::************:user/data-access.lazar.lazarevic
Created: 2025-03-04T15:44:16+00:00
Inline Policies: 0
Attached Policies: 0
Groups: None
Last Activity: 2025-03-11T11:26:02+00:00

      Analyzing user: data-access.mikhail.kniazev

--- User: data-access.mikhail.kniazev (human-user) ---
Account: ************ - aws-account-ml
ARN: arn:aws:iam::************:user/data-access.mikhail.kniazev
Created: 2025-03-04T14:50:07+00:00
Inline Policies: 0
Attached Policies: 0
Groups: None
Last Activity: 2025-03-12T11:56:36+00:00

      Analyzing user: data-access.nikita.chernata

--- User: data-access.nikita.chernata (human-user) ---
Account: ************ - aws-account-ml
ARN: arn:aws:iam::************:user/data-access.nikita.chernata
Created: 2025-03-04T15:55:28+00:00
Inline Policies: 0
Attached Policies: 0
Groups: None
Last Activity: 2025-03-19T13:35:51+00:00

      Analyzing user: data-access.nipun.garg

--- User: data-access.nipun.garg (human-user) ---
Account: ************ - aws-account-ml
ARN: arn:aws:iam::************:user/data-access.nipun.garg
Created: 2025-03-04T15:54:35+00:00
Inline Policies: 0
Attached Policies: 0
Groups: None
Last Activity: 2025-03-11T07:45:41+00:00

      Analyzing user: data-access.pavel.arzhaev

--- User: data-access.pavel.arzhaev (human-user) ---
Account: ************ - aws-account-ml
ARN: arn:aws:iam::************:user/data-access.pavel.arzhaev
Created: 2025-03-04T14:51:05+00:00
Inline Policies: 0
Attached Policies: 0
Groups: None
Last Activity: 2025-03-10T16:30:40+00:00

      Analyzing user: data-access.pavel.kaloshin

--- User: data-access.pavel.kaloshin (human-user) ---
Account: ************ - aws-account-ml
ARN: arn:aws:iam::************:user/data-access.pavel.kaloshin
Created: 2025-03-04T15:28:58+00:00
Inline Policies: 0
Attached Policies: 0
Groups: None
Last Activity: 2025-03-10T13:14:08+00:00

      Analyzing user: data-access.renat.khizbullin

--- User: data-access.renat.khizbullin (human-user) ---
Account: ************ - aws-account-ml
ARN: arn:aws:iam::************:user/data-access.renat.khizbullin
Created: 2025-03-04T15:45:49+00:00
Inline Policies: 0
Attached Policies: 0
Groups: None
Last Activity: 2025-03-24T11:59:19+00:00

      Analyzing user: data-access.rinat.kuzmin

--- User: data-access.rinat.kuzmin (human-user) ---
Account: ************ - aws-account-ml
ARN: arn:aws:iam::************:user/data-access.rinat.kuzmin
Created: 2025-03-04T14:52:01+00:00
Inline Policies: 0
Attached Policies: 0
Groups: None
Last Activity: 2025-03-18T12:09:35+00:00

      Analyzing user: data-access.rustem.zalyalov

--- User: data-access.rustem.zalyalov (human-user) ---
Account: ************ - aws-account-ml
ARN: arn:aws:iam::************:user/data-access.rustem.zalyalov
Created: 2025-03-04T15:22:45+00:00
Inline Policies: 0
Attached Policies: 0
Groups: None
Last Activity: 2025-03-04T15:38:01+00:00

      Analyzing user: data-access.serge.chavez

--- User: data-access.serge.chavez (human-user) ---
Account: ************ - aws-account-ml
ARN: arn:aws:iam::************:user/data-access.serge.chavez
Created: 2025-03-04T15:54:08+00:00
Inline Policies: 0
Attached Policies: 0
Groups: None
Last Activity: 2025-03-10T10:09:09+00:00

      Analyzing user: data-access.svetlana.volkova

--- User: data-access.svetlana.volkova (human-user) ---
Account: ************ - aws-account-ml
ARN: arn:aws:iam::************:user/data-access.svetlana.volkova
Created: 2025-03-04T15:21:50+00:00
Inline Policies: 0
Attached Policies: 0
Groups: None
Last Activity: 2025-03-10T10:58:48+00:00

      Analyzing user: data-access.valeriia.koriukina

--- User: data-access.valeriia.koriukina (human-user) ---
Account: ************ - aws-account-ml
ARN: arn:aws:iam::************:user/data-access.valeriia.koriukina
Created: 2025-04-30T05:17:02+00:00
Inline Policies: 0
Attached Policies: 0
Groups: None
Last Activity: 2025-04-30T08:52:57+00:00

      Analyzing user: data-access.vasilije.pantic

--- User: data-access.vasilije.pantic (human-user) ---
Account: ************ - aws-account-ml
ARN: arn:aws:iam::************:user/data-access.vasilije.pantic
Created: 2025-03-04T15:46:20+00:00
Inline Policies: 0
Attached Policies: 0
Groups: None
Last Activity: 2025-03-10T12:29:57+00:00

      Analyzing user: data-access.vusal.salmanov

--- User: data-access.vusal.salmanov (human-user) ---
Account: ************ - aws-account-ml
ARN: arn:aws:iam::************:user/data-access.vusal.salmanov
Created: 2025-03-04T15:56:42+00:00
Inline Policies: 0
Attached Policies: 0
Groups: None
Last Activity: No keys

      Analyzing user: pavel.arzhaev

--- User: pavel.arzhaev (human-user) ---
Account: ************ - aws-account-ml
ARN: arn:aws:iam::************:user/pavel.arzhaev
Created: 2022-05-27T14:28:37+00:00
Inline Policies: 0
Attached Policies: 1
Groups: None
Last Activity: 2022-07-04T13:13:25+00:00

    ✅ Successfully processed 35 users

🔍 Processing Account: ************ (aws-account-labs) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountVPCManagementRole
    External ID: DrataCleanup2025
    ✅ Successfully assumed role and validated credentials
  ✅ Successfully assumed role

📋 Listing IAM users in Account: ************ (aws-account-labs) - Status: ACTIVE
    Found 0 IAM users
    No IAM users found in this account

🔍 Processing Account: ************ (<EMAIL>) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountVPCManagementRole
    External ID: DrataCleanup2025
    ✅ Successfully assumed role and validated credentials
  ✅ Successfully assumed role

📋 Listing IAM users in Account: ************ (<EMAIL>) - Status: ACTIVE
    Found 1 IAM users
      Analyzing user: updraftplus-backup-user

--- User: updraftplus-backup-user (other) ---
Account: ************ - <EMAIL>
ARN: arn:aws:iam::************:user/updraftplus-backup-user
Created: 2025-02-05T12:45:28+00:00
Inline Policies: 0
Attached Policies: 0
Groups: None
Last Activity: 2025-04-21T20:01:22+00:00

    ✅ Successfully processed 1 users

🔍 Processing Account: ************ (AWS-account-labelling-sandbox) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountVPCManagementRole
    External ID: DrataCleanup2025
    ✅ Successfully assumed role and validated credentials
  ✅ Successfully assumed role

📋 Listing IAM users in Account: ************ (AWS-account-labelling-sandbox) - Status: ACTIVE
    Found 1 IAM users
      Analyzing user: github-action-terraform-service

--- User: github-action-terraform-service (github-service) ---
Account: ************ - AWS-account-labelling-sandbox
ARN: arn:aws:iam::************:user/github-action-terraform-service
Created: 2024-02-07T14:48:47+00:00
Inline Policies: 0
Attached Policies: 1
Groups: None
Last Activity: 2024-02-07T14:49:20+00:00

    ✅ Successfully processed 1 users

🔍 Processing Account: ************ (aws-security) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountVPCManagementRole
    External ID: DrataCleanup2025
    ✅ Successfully assumed role and validated credentials
  ✅ Successfully assumed role

📋 Listing IAM users in Account: ************ (aws-security) - Status: ACTIVE
    Found 2 IAM users
      Analyzing user: 1password-scim-terraform-user

--- User: 1password-scim-terraform-user (terraform-service) ---
Account: ************ - aws-security
ARN: arn:aws:iam::************:user/1password-scim-terraform-user
Created: 2024-05-14T15:32:47+00:00
Inline Policies: 0
Attached Policies: 10
Groups: None
Last Activity: 2024-05-14T15:33:16+00:00

      Analyzing user: motorway-poc-dataset-user

--- User: motorway-poc-dataset-user (other) ---
Account: ************ - aws-security
ARN: arn:aws:iam::************:user/motorway-poc-dataset-user
Created: 2025-03-06T21:19:09+00:00
Inline Policies: 0
Attached Policies: 1
Groups: None
Last Activity: 2025-03-06T21:19:32+00:00

    ✅ Successfully processed 2 users

🔍 Processing Account: ************ (Mati Biometrics) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountVPCManagementRole
    External ID: DrataCleanup2025
    ✅ Successfully assumed role and validated credentials
  ✅ Successfully assumed role

📋 Listing IAM users in Account: ************ (Mati Biometrics) - Status: ACTIVE
    Found 27 IAM users
      Analyzing user: analytics-s3-access

--- User: analytics-s3-access (other) ---
Account: ************ - Mati Biometrics
ARN: arn:aws:iam::************:user/analytics-s3-access
Created: 2022-03-25T14:12:09+00:00
Inline Policies: 0
Attached Policies: 1
Groups: None
Last Activity: 2023-09-21T14:33:26+00:00

      Analyzing user: att-media-eu

--- User: att-media-eu (other) ---
Account: ************ - Mati Biometrics
ARN: arn:aws:iam::************:user/att-media-eu
Created: 2024-03-15T13:53:33+00:00
Inline Policies: 0
Attached Policies: 1
Groups: None
Last Activity: 2024-03-15T13:53:50+00:00

      Analyzing user: auth0-smtp-user

--- User: auth0-smtp-user (other) ---
Account: ************ - Mati Biometrics
ARN: arn:aws:iam::************:user/auth0-smtp-user
Created: 2023-04-05T10:12:35+00:00
Inline Policies: 1
Attached Policies: 0
Groups: None
Last Activity: 2023-04-05T10:12:36+00:00

      Analyzing user: deployment-automation

--- User: deployment-automation (other) ---
Account: ************ - Mati Biometrics
ARN: arn:aws:iam::************:user/deployment-automation
Created: 2018-07-31T14:07:26+00:00
Inline Policies: 0
Attached Policies: 2
Groups: None
Last Activity: 2023-09-21T01:30:54+00:00

      Analyzing user: deployment-automation-temp

--- User: deployment-automation-temp (other) ---
Account: ************ - Mati Biometrics
ARN: arn:aws:iam::************:user/deployment-automation-temp
Created: 2022-06-14T05:02:31+00:00
Inline Policies: 0
Attached Policies: 2
Groups: None
Last Activity: 2022-06-14T05:02:32+00:00

      Analyzing user: dev-domain-validator

--- User: dev-domain-validator (other) ---
Account: ************ - Mati Biometrics
ARN: arn:aws:iam::************:user/dev-domain-validator
Created: 2019-06-27T20:48:47+00:00
Inline Policies: 0
Attached Policies: 1
Groups: None
Last Activity: 2019-06-27T20:48:49+00:00

      Analyzing user: elastic-snapshot

--- User: elastic-snapshot (other) ---
Account: ************ - Mati Biometrics
ARN: arn:aws:iam::************:user/elastic-snapshot
Created: 2022-11-29T17:30:16+00:00
Inline Policies: 0
Attached Policies: 1
Groups: None
Last Activity: 2022-11-29T17:30:18+00:00

      Analyzing user: frontend-bucket-staging-user

--- User: frontend-bucket-staging-user (other) ---
Account: ************ - Mati Biometrics
ARN: arn:aws:iam::************:user/frontend-bucket-staging-user
Created: 2022-11-02T12:14:32+00:00
Inline Policies: 0
Attached Policies: 0
Groups: None
Last Activity: 2022-11-02T12:14:33+00:00

      Analyzing user: gitlab

--- User: gitlab (other) ---
Account: ************ - Mati Biometrics
ARN: arn:aws:iam::************:user/gitlab
Created: 2022-05-23T15:42:07+00:00
Inline Policies: 0
Attached Policies: 0
Groups: None
Last Activity: 2022-12-25T18:44:33+00:00

      Analyzing user: gitlab-ci

--- User: gitlab-ci (other) ---
Account: ************ - Mati Biometrics
ARN: arn:aws:iam::************:user/gitlab-ci
Created: 2022-03-24T19:08:42+00:00
Inline Policies: 1
Attached Policies: 2
Groups: None
Last Activity: 2022-03-28T13:19:39+00:00

      Analyzing user: google-gpu-key

--- User: google-gpu-key (other) ---
Account: ************ - Mati Biometrics
ARN: arn:aws:iam::************:user/google-gpu-key
Created: 2019-09-07T17:41:03+00:00
Inline Policies: 1
Attached Policies: 5
Groups: None
Last Activity: 2023-09-21T00:51:50+00:00

      Analyzing user: grafana

--- User: grafana (other) ---
Account: ************ - Mati Biometrics
ARN: arn:aws:iam::************:user/grafana
Created: 2021-12-16T11:45:40+00:00
Inline Policies: 1
Attached Policies: 0
Groups: None
Last Activity: 2023-09-21T00:55:40+00:00

      Analyzing user: hostkey-gpu

--- User: hostkey-gpu (other) ---
Account: ************ - Mati Biometrics
ARN: arn:aws:iam::************:user/hostkey-gpu
Created: 2018-12-12T09:12:57+00:00
Inline Policies: 0
Attached Policies: 1
Groups: None
Last Activity: 2023-09-21T00:59:58+00:00

      Analyzing user: mas-frontend

--- User: mas-frontend (other) ---
Account: ************ - Mati Biometrics
ARN: arn:aws:iam::************:user/mas-frontend
Created: 2022-06-16T09:04:02+00:00
Inline Policies: 0
Attached Policies: 1
Groups: None
Last Activity: 2022-06-16T09:04:03+00:00

      Analyzing user: messaging-service-app-user

--- User: messaging-service-app-user (other) ---
Account: ************ - Mati Biometrics
ARN: arn:aws:iam::************:user/messaging-service-app-user
Created: 2022-12-13T11:01:34+00:00
Inline Policies: 0
Attached Policies: 1
Groups: None
Last Activity: 2022-12-13T11:01:35+00:00

      Analyzing user: metamap-security-team

--- User: metamap-security-team (other) ---
Account: ************ - Mati Biometrics
ARN: arn:aws:iam::************:user/metamap-security-team
Created: 2023-07-25T11:33:11+00:00
Inline Policies: 0
Attached Policies: 0
Groups: None
Last Activity: 2023-11-06T14:16:24+00:00

      Analyzing user: piyush.jain

--- User: piyush.jain (human-user) ---
Account: ************ - Mati Biometrics
ARN: arn:aws:iam::************:user/piyush.jain
Created: 2021-09-14T05:46:35+00:00
Inline Policies: 1
Attached Policies: 0
Groups: None
Last Activity: 2023-10-06T17:48:19+00:00

      Analyzing user: <EMAIL>

--- User: <EMAIL> (human-user) ---
Account: ************ - Mati Biometrics
ARN: arn:aws:iam::************:user/<EMAIL>
Created: 2024-08-01T06:01:39+00:00
Inline Policies: 0
Attached Policies: 1
Groups: None
Last Activity: 2024-09-10T01:41:47+00:00

      Analyzing user: serverless-pdf-generator

--- User: serverless-pdf-generator (other) ---
Account: ************ - Mati Biometrics
ARN: arn:aws:iam::************:user/serverless-pdf-generator
Created: 2022-05-04T06:47:33+00:00
Inline Policies: 2
Attached Policies: 3
Groups: None
Last Activity: 2022-05-04T06:47:35+00:00

      Analyzing user: service_user_label_studio

--- User: service_user_label_studio (other) ---
Account: ************ - Mati Biometrics
ARN: arn:aws:iam::************:user/service_user_label_studio
Created: 2022-08-30T11:50:14+00:00
Inline Policies: 0
Attached Policies: 2
Groups: None
Last Activity: 2023-09-21T01:01:29+00:00

      Analyzing user: ses-smtp-user.********-154404

--- User: ses-smtp-user.********-154404 (human-user) ---
Account: ************ - Mati Biometrics
ARN: arn:aws:iam::************:user/ses-smtp-user.********-154404
Created: 2023-02-07T10:14:09+00:00
Inline Policies: 1
Attached Policies: 0
Groups: None
Last Activity: 2023-04-04T15:14:28+00:00

      Analyzing user: ses-smtp-user.dev-01

--- User: ses-smtp-user.dev-01 (human-user) ---
Account: ************ - Mati Biometrics
ARN: arn:aws:iam::************:user/ses-smtp-user.dev-01
Created: 2019-01-17T11:14:17+00:00
Inline Policies: 1
Attached Policies: 0
Groups: None
Last Activity: 2019-01-17T11:14:17+00:00

      Analyzing user: ses-user-stage

--- User: ses-user-stage (other) ---
Account: ************ - Mati Biometrics
ARN: arn:aws:iam::************:user/ses-user-stage
Created: 2022-01-20T10:34:19+00:00
Inline Policies: 0
Attached Policies: 1
Groups: None
Last Activity: 2022-01-20T10:34:21+00:00

      Analyzing user: temp_user_file_upload_fix

--- User: temp_user_file_upload_fix (other) ---
Account: ************ - Mati Biometrics
ARN: arn:aws:iam::************:user/temp_user_file_upload_fix
Created: 2023-08-26T10:14:42+00:00
Inline Policies: 1
Attached Policies: 0
Groups: None
Last Activity: 2023-08-26T12:27:24+00:00

      Analyzing user: vald-bucket

--- User: vald-bucket (other) ---
Account: ************ - Mati Biometrics
ARN: arn:aws:iam::************:user/vald-bucket
Created: 2022-03-10T10:37:54+00:00
Inline Policies: 0
Attached Policies: 1
Groups: None
Last Activity: 2022-03-10T10:37:55+00:00

      Analyzing user: vision-media

--- User: vision-media (other) ---
Account: ************ - Mati Biometrics
ARN: arn:aws:iam::************:user/vision-media
Created: 2021-11-29T10:17:24+00:00
Inline Policies: 0
Attached Policies: 1
Groups: None
Last Activity: 2023-09-21T00:56:48+00:00

      Analyzing user: vision-team-local-development

--- User: vision-team-local-development (other) ---
Account: ************ - Mati Biometrics
ARN: arn:aws:iam::************:user/vision-team-local-development
Created: 2023-11-24T06:21:37+00:00
Inline Policies: 1
Attached Policies: 1
Groups: None
Last Activity: 2023-11-24T06:21:58+00:00

    ✅ Successfully processed 27 users

🔍 Processing Account: ************ (incode_staging) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountVPCManagementRole
    External ID: DrataCleanup2025
    ✅ Successfully assumed role and validated credentials
  ✅ Successfully assumed role

📋 Listing IAM users in Account: ************ (incode_staging) - Status: ACTIVE
    Found 18 IAM users
      Analyzing user: batch-runner

--- User: batch-runner (other) ---
Account: ************ - incode_staging
ARN: arn:aws:iam::************:user/batch-runner
Created: 2025-01-09T20:26:56+00:00
Inline Policies: 1
Attached Policies: 1
Groups: None
Last Activity: 2025-01-09T20:30:45+00:00

      Analyzing user: cert-managaer-route53

--- User: cert-managaer-route53 (other) ---
Account: ************ - incode_staging
ARN: arn:aws:iam::************:user/cert-managaer-route53
Created: 2023-09-18T16:27:42+00:00
Inline Policies: 0
Attached Policies: 3
Groups: None
Last Activity: 2023-09-18T16:30:34+00:00

      Analyzing user: github-actions-backend

--- User: github-actions-backend (github-service) ---
Account: ************ - incode_staging
ARN: arn:aws:iam::************:user/github-actions-backend
Created: 2023-08-04T09:16:39+00:00
Inline Policies: 0
Attached Policies: 4
Groups: None
Last Activity: 2023-08-04T09:17:02+00:00

      Analyzing user: github-actions-chat-service

--- User: github-actions-chat-service (github-service) ---
Account: ************ - incode_staging
ARN: arn:aws:iam::************:user/github-actions-chat-service
Created: 2023-06-15T00:37:21+00:00
Inline Policies: 0
Attached Policies: 1
Groups: None
Last Activity: 2023-06-15T00:38:24+00:00

      Analyzing user: github-actions-web

--- User: github-actions-web (github-service) ---
Account: ************ - incode_staging
ARN: arn:aws:iam::************:user/github-actions-web
Created: 2023-06-01T00:01:12+00:00
Inline Policies: 0
Attached Policies: 1
Groups: None
Last Activity: 2023-06-01T00:02:16+00:00

      Analyzing user: Github-runner-for-jmeter

--- User: Github-runner-for-jmeter (other) ---
Account: ************ - incode_staging
ARN: arn:aws:iam::************:user/Github-runner-for-jmeter
Created: 2023-04-03T15:41:19+00:00
Inline Policies: 0
Attached Policies: 1
Groups: None
Last Activity: 2023-04-03T15:41:48+00:00

      Analyzing user: github-terraform-serv-acc

--- User: github-terraform-serv-acc (github-service) ---
Account: ************ - incode_staging
ARN: arn:aws:iam::************:user/github-terraform-serv-acc
Created: 2022-12-19T18:34:40+00:00
Inline Policies: 0
Attached Policies: 1
Groups: None
Last Activity: 2022-12-19T18:34:41+00:00

      Analyzing user: incode-os-user-service

--- User: incode-os-user-service (other) ---
Account: ************ - incode_staging
ARN: arn:aws:iam::************:user/incode-os-user-service
Created: 2023-04-04T19:41:48+00:00
Inline Policies: 0
Attached Policies: 2
Groups: None
Last Activity: 2023-04-04T19:42:23+00:00

      Analyzing user: incode-s3

--- User: incode-s3 (other) ---
Account: ************ - incode_staging
ARN: arn:aws:iam::************:user/incode-s3
Created: 2023-12-06T16:12:35+00:00
Inline Policies: 0
Attached Policies: 3
Groups: None
Last Activity: 2024-04-22T18:55:35+00:00

      Analyzing user: incodeid-github-actions

--- User: incodeid-github-actions (other) ---
Account: ************ - incode_staging
ARN: arn:aws:iam::************:user/incodeid-github-actions
Created: 2023-05-30T23:38:37+00:00
Inline Policies: 0
Attached Policies: 1
Groups: None
Last Activity: 2023-05-30T23:39:14+00:00

      Analyzing user: IncodeMongoDB2ElasticServiceStageUS

--- User: IncodeMongoDB2ElasticServiceStageUS (other) ---
Account: ************ - incode_staging
ARN: arn:aws:iam::************:user/IncodeMongoDB2ElasticServiceStageUS
Created: 2023-06-20T00:00:29+00:00
Inline Policies: 0
Attached Policies: 0
Groups: None
Last Activity: 2023-06-20T00:01:20+00:00

      Analyzing user: IncodeUserServiceStageUS

--- User: IncodeUserServiceStageUS (other) ---
Account: ************ - incode_staging
ARN: arn:aws:iam::************:user/Incode/IncodeUserServiceStageUS
Created: 2022-12-05T22:36:31+00:00
Inline Policies: 0
Attached Policies: 5
Groups: None
Last Activity: 2024-09-04T00:04:10+00:00

      Analyzing user: keycloak-a7vmz4gr-sa

--- User: keycloak-a7vmz4gr-sa (keycloak-service) ---
Account: ************ - incode_staging
ARN: arn:aws:iam::************:user/keycloak-a7vmz4gr-sa
Created: 2024-06-19T10:54:50+00:00
Inline Policies: 0
Attached Policies: 1
Groups: None
Last Activity: 2024-06-19T10:54:58+00:00

      Analyzing user: lazar.slavkovic

--- User: lazar.slavkovic (human-user) ---
Account: ************ - incode_staging
ARN: arn:aws:iam::************:user/lazar.slavkovic
Created: 2025-05-07T17:31:17+00:00
Inline Policies: 0
Attached Policies: 1
Groups: None
Last Activity: 2025-05-07T17:31:44+00:00

      Analyzing user: rancher-service-user

--- User: rancher-service-user (other) ---
Account: ************ - incode_staging
ARN: arn:aws:iam::************:user/rancher-service-user
Created: 2023-07-31T15:28:58+00:00
Inline Policies: 0
Attached Policies: 0
Groups: None
Last Activity: 2023-07-31T15:29:26+00:00

      Analyzing user: serv-acc-jmeter

--- User: serv-acc-jmeter (other) ---
Account: ************ - incode_staging
ARN: arn:aws:iam::************:user/serv-acc-jmeter
Created: 2022-12-12T18:29:52+00:00
Inline Policies: 0
Attached Policies: 1
Groups: None
Last Activity: 2022-12-12T18:29:52+00:00

      Analyzing user: terraform-test

--- User: terraform-test (terraform-service) ---
Account: ************ - incode_staging
ARN: arn:aws:iam::************:user/terraform-test
Created: 2023-02-23T19:30:34+00:00
Inline Policies: 0
Attached Policies: 1
Groups: None
Last Activity: 2023-02-23T19:31:30+00:00

      Analyzing user: tokbox_vonage_integration_new_staging

--- User: tokbox_vonage_integration_new_staging (vonage-service) ---
Account: ************ - incode_staging
ARN: arn:aws:iam::************:user/tokbox_vonage_integration_new_staging
Created: 2023-09-05T06:42:25+00:00
Inline Policies: 0
Attached Policies: 1
Groups: None
Last Activity: 2023-09-05T06:42:52+00:00

    ✅ Successfully processed 18 users
