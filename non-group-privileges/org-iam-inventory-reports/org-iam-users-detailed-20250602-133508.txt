AWS Organization IAM Users Inventory Report
Generated: Mon Jun  2 13:35:10 CST 2025
Management Account: ************

This report contains ALL IAM users across the entire AWS Organization.

=== INVENTORY RESULTS ===


🔍 Processing Account: ************ (<EMAIL>) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountVPCManagementRole
    External ID: DrataCleanup2025
    ✅ Successfully assumed role and validated credentials
  ✅ Successfully assumed role

📋 Listing IAM users in Account: ************ (<EMAIL>) - Status: ACTIVE
    Found 3 IAM users
      Analyzing user: github-terraform-serv-acc

--- User: github-terraform-serv-acc (github-service) ---
Account: ************ - <EMAIL>
ARN: arn:aws:iam::************:user/github-terraform-serv-acc
Created: 2024-08-14T01:26:28+00:00
Inline Policies: 0
Attached Policies: 2
Groups: None
Last Activity: 2024-08-14T01:30:10+00:00

      Analyzing user: keycloak-sxdb5pw5-sa

--- User: keycloak-sxdb5pw5-sa (keycloak-service) ---
Account: ************ - <EMAIL>
ARN: arn:aws:iam::************:user/keycloak-sxdb5pw5-sa
Created: 2024-08-20T06:26:29+00:00
Inline Policies: 0
Attached Policies: 1
Groups: None
Last Activity: 2024-08-20T06:26:29+00:00

      Analyzing user: tokbox_vonage_integration

--- User: tokbox_vonage_integration (vonage-service) ---
Account: ************ - <EMAIL>
ARN: arn:aws:iam::************:user/tokbox_vonage_integration
Created: 2024-08-19T07:26:32+00:00
Inline Policies: 0
Attached Policies: 1
Groups: None
Last Activity: 2024-08-19T07:37:29+00:00

    ✅ Successfully processed 3 users
⚠️  Account ************ processing failed, continuing with next account...

🔍 Processing Account: ************ (Certificate Authorities) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountVPCManagementRole
    External ID: DrataCleanup2025
    ✅ Successfully assumed role and validated credentials
  ✅ Successfully assumed role

📋 Listing IAM users in Account: ************ (Certificate Authorities) - Status: ACTIVE
    Found 0 IAM users
    No IAM users found in this account

🔍 Processing Account: ************ (incode-bridge) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountVPCManagementRole
    External ID: DrataCleanup2025
    ✅ Successfully assumed role and validated credentials
  ✅ Successfully assumed role

📋 Listing IAM users in Account: ************ (incode-bridge) - Status: ACTIVE
    Found 0 IAM users
    No IAM users found in this account
