Account ID,Account Name,Account Status,User Name,User ARN,User Type,Created Date,Inline Policies,Attached Policies,Groups,Last Activity,Access Status
************,"<EMAIL>",ACTIVE,ROLE_FAILED,N/A,N/A,N/A,0,0,"",N/A,NO_ACCESS
************,"Certificate Authorities",ACTIVE,ROLE_FAILED,N/A,N/A,N/A,0,0,"",N/A,NO_ACCESS
************,"incode-bridge",ACTIVE,ROLE_FAILED,N/A,N/A,N/A,0,0,"",N/A,NO_ACCESS
************,"Incode Technologies T2 9688",ACTIVE,ROLE_FAILED,N/A,N/A,N/A,0,0,"",N/A,NO_ACCESS
************,"<EMAIL>",ACTIVE,ROLE_FAILED,N/A,N/A,N/A,0,0,"",N/A,NO_ACCESS
************,"aws-account-customer-success-tooling",ACTIVE,ROLE_FAILED,N/A,N/A,N/A,0,0,"",N/A,NO_ACCESS
************,"incode_logging",ACTIVE,ROLE_FAILED,N/A,N/A,N/A,0,0,"",N/A,NO_ACCESS
************,"AWS-account-networking",ACTIVE,ROLE_FAILED,N/A,N/A,N/A,0,0,"",N/A,NO_ACCESS
************,"AWS-account-operations",ACTIVE,ROLE_FAILED,N/A,N/A,N/A,0,0,"",N/A,NO_ACCESS
************,"AWS Account Canada SAAS",ACTIVE,ROLE_FAILED,N/A,N/A,N/A,0,0,"",N/A,NO_ACCESS
************,"Incode Technologies T1  4717",ACTIVE,NO_USERS,N/A,N/A,N/A,0,0,"",N/A,SUCCESS
************,"Operations",ACTIVE,ROLE_FAILED,N/A,N/A,N/A,0,0,"",N/A,NO_ACCESS
************,"<EMAIL>",ACTIVE,ROLE_FAILED,N/A,N/A,N/A,0,0,"",N/A,NO_ACCESS
************,"Metamap Staging",ACTIVE,ROLE_FAILED,N/A,N/A,N/A,0,0,"",N/A,NO_ACCESS
************,"AWS-account-development",ACTIVE,ROLE_FAILED,N/A,N/A,N/A,0,0,"",N/A,NO_ACCESS
************,"AWS-account-sre-mgmt",ACTIVE,ROLE_FAILED,N/A,N/A,N/A,0,0,"",N/A,NO_ACCESS
************,"Microformas_Citi",ACTIVE,ROLE_FAILED,N/A,N/A,N/A,0,0,"",N/A,NO_ACCESS
************,"AWS-account-omni-shared-services",ACTIVE,ROLE_FAILED,N/A,N/A,N/A,0,0,"",N/A,NO_ACCESS
************,"aws-account-demo-k8s",ACTIVE,ROLE_FAILED,N/A,N/A,N/A,0,0,"",N/A,NO_ACCESS
************,"<EMAIL>",ACTIVE,ROLE_FAILED,N/A,N/A,N/A,0,0,"",N/A,NO_ACCESS
************,"aws-account-product-ops-tooling",ACTIVE,ROLE_FAILED,N/A,N/A,N/A,0,0,"",N/A,NO_ACCESS
************,"AWS-account-filesharing",ACTIVE,ROLE_FAILED,N/A,N/A,N/A,0,0,"",N/A,NO_ACCESS
************,"Metamap Development",ACTIVE,ROLE_FAILED,N/A,N/A,N/A,0,0,"",N/A,NO_ACCESS
************,"<EMAIL>",ACTIVE,ROLE_FAILED,N/A,N/A,N/A,0,0,"",N/A,NO_ACCESS
************,"<EMAIL>",ACTIVE,ROLE_FAILED,N/A,N/A,N/A,0,0,"",N/A,NO_ACCESS
************,"<EMAIL>",ACTIVE,ROLE_FAILED,N/A,N/A,N/A,0,0,"",N/A,NO_ACCESS
************,"<EMAIL>",ACTIVE,ROLE_FAILED,N/A,N/A,N/A,0,0,"",N/A,NO_ACCESS
************,"<EMAIL>",ACTIVE,ROLE_FAILED,N/A,N/A,N/A,0,0,"",N/A,NO_ACCESS
************,"Metamap Data",ACTIVE,ROLE_FAILED,N/A,N/A,N/A,0,0,"",N/A,NO_ACCESS
************,"AWS-account-FL-HSVM-pilot",ACTIVE,ROLE_FAILED,N/A,N/A,N/A,0,0,"",N/A,NO_ACCESS
************,"Athena Production",ACTIVE,ROLE_FAILED,N/A,N/A,N/A,0,0,"",N/A,NO_ACCESS
************,"ct-log-audit",ACTIVE,ROLE_FAILED,N/A,N/A,N/A,0,0,"",N/A,NO_ACCESS
************,"<EMAIL>",ACTIVE,ROLE_FAILED,N/A,N/A,N/A,0,0,"",N/A,NO_ACCESS
************,"aws-account-citimx-dev",ACTIVE,ROLE_FAILED,N/A,N/A,N/A,0,0,"",N/A,NO_ACCESS
************,"Metamap AI",ACTIVE,ROLE_FAILED,N/A,N/A,N/A,0,0,"",N/A,NO_ACCESS
************,"ct-log-archive",ACTIVE,ROLE_FAILED,N/A,N/A,N/A,0,0,"",N/A,NO_ACCESS
************,"Metamap Production",ACTIVE,ROLE_FAILED,N/A,N/A,N/A,0,0,"",N/A,NO_ACCESS
************,"AWS-account-CA-DMV-pilot",ACTIVE,ROLE_FAILED,N/A,N/A,N/A,0,0,"",N/A,NO_ACCESS
************,"CitiBanamex Analytics Account",ACTIVE,ROLE_FAILED,N/A,N/A,N/A,0,0,"",N/A,NO_ACCESS
************,"AWS-account-demo",ACTIVE,ROLE_FAILED,N/A,N/A,N/A,0,0,"",N/A,NO_ACCESS
************,"Athena Devel",ACTIVE,ROLE_FAILED,N/A,N/A,N/A,0,0,"",N/A,NO_ACCESS
************,"AWS-account-pollid",ACTIVE,ROLE_FAILED,N/A,N/A,N/A,0,0,"",N/A,NO_ACCESS
************,"AWS-account-georgia-dds-stage",ACTIVE,ROLE_FAILED,N/A,N/A,N/A,0,0,"",N/A,NO_ACCESS
************,"aws-account-citimx-prod",ACTIVE,ROLE_FAILED,N/A,N/A,N/A,0,0,"",N/A,NO_ACCESS
************,"<EMAIL>",ACTIVE,ROLE_FAILED,N/A,N/A,N/A,0,0,"",N/A,NO_ACCESS
************,"aws-account-canada-k8s",ACTIVE,ROLE_FAILED,N/A,N/A,N/A,0,0,"",N/A,NO_ACCESS
************,"AWS-account-saas-australia",ACTIVE,ROLE_FAILED,N/A,N/A,N/A,0,0,"",N/A,NO_ACCESS
************,"AWS Account Colombia SOR",ACTIVE,ROLE_FAILED,N/A,N/A,N/A,0,0,"",N/A,NO_ACCESS
************,"aws-account-ml",ACTIVE,ROLE_FAILED,N/A,N/A,N/A,0,0,"",N/A,NO_ACCESS
************,"aws-account-labs",ACTIVE,ROLE_FAILED,N/A,N/A,N/A,0,0,"",N/A,NO_ACCESS
************,"<EMAIL>",ACTIVE,ROLE_FAILED,N/A,N/A,N/A,0,0,"",N/A,NO_ACCESS
************,"AWS-account-labelling-sandbox",ACTIVE,ROLE_FAILED,N/A,N/A,N/A,0,0,"",N/A,NO_ACCESS
************,"aws-security",ACTIVE,ROLE_FAILED,N/A,N/A,N/A,0,0,"",N/A,NO_ACCESS
************,"Mati Biometrics",ACTIVE,ROLE_FAILED,N/A,N/A,N/A,0,0,"",N/A,NO_ACCESS
************,"incode_staging",ACTIVE,ROLE_FAILED,N/A,N/A,N/A,0,0,"",N/A,NO_ACCESS
