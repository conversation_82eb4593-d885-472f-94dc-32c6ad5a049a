AWS Organization IAM Users Inventory Statistics
Generated: Mon Jun  2 13:35:29 CST 2025
Management Account: ************

=== ACCOUNT SUMMARY ===
Total Accounts in Organization: 57
Active Accounts: 55
Successfully Processed: 3
Failed to Access: 0

=== USER SUMMARY ===
Total IAM Users Found: 3

=== USER TYPE BREAKDOWN ===
GitHub Service Accounts: 1
Keycloak Service Accounts: 1
Terraform Service Accounts: 0
Other Service Accounts: 0
Human Users: 0
Other/Unknown: 1

=== FILES GENERATED ===
Detailed Report: /Users/<USER>/Incode/source/aws-stackset-inventory/non-group-privileges/org-iam-inventory-reports/org-iam-users-detailed-********-133508.txt
Summary CSV: /Users/<USER>/Incode/source/aws-stackset-inventory/non-group-privileges/org-iam-inventory-reports/org-iam-users-summary-********-133508.csv
Statistics: /Users/<USER>/Incode/source/aws-stackset-inventory/non-group-privileges/org-iam-inventory-reports/org-iam-users-stats-********-133508.txt
Accounts List: /Users/<USER>/Incode/source/aws-stackset-inventory/non-group-privileges/org-iam-inventory-reports/org-accounts-********-133508.txt

=== NEXT STEPS ===
1. Review the detailed report for complete user information
2. Analyze the summary CSV for patterns and compliance issues
3. Use the data for IAM governance and security reviews
4. Consider group-based access control migration where appropriate

