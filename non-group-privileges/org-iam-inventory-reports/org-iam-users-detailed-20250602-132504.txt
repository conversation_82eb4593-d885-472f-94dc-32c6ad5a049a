AWS Organization IAM Users Inventory Report
Generated: Mon Jun  2 13:25:06 CST 2025
Management Account: ************

This report contains ALL IAM users across the entire AWS Organization.

=== INVENTORY RESULTS ===


🔍 Processing Account: ************ (<EMAIL>) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountAuditRole
    ❌ Invalid JSON response from assume-role
  ❌ Failed to assume role - skipping account
⚠️  Account ************ processing failed, continuing with next account...

🔍 Processing Account: ************ (Certificate Authorities) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountAuditRole
    ❌ Invalid JSON response from assume-role
  ❌ Failed to assume role - skipping account
⚠️  Account ************ processing failed, continuing with next account...

🔍 Processing Account: ************ (incode-bridge) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountAuditRole
    ❌ Invalid JSON response from assume-role
  ❌ Failed to assume role - skipping account
⚠️  Account ************ processing failed, continuing with next account...

🔍 Processing Account: ************ (Incode Technologies T2 9688) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountAuditRole
    ❌ Invalid JSON response from assume-role
  ❌ Failed to assume role - skipping account
⚠️  Account ************ processing failed, continuing with next account...

🔍 Processing Account: ************ (<EMAIL>) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountAuditRole
    ❌ Invalid JSON response from assume-role
  ❌ Failed to assume role - skipping account
⚠️  Account ************ processing failed, continuing with next account...

🔍 Processing Account: ************ (aws-account-customer-success-tooling) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountAuditRole
    ❌ Invalid JSON response from assume-role
  ❌ Failed to assume role - skipping account
⚠️  Account ************ processing failed, continuing with next account...

🔍 Processing Account: ************ (incode_logging) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountAuditRole
    ❌ Invalid JSON response from assume-role
  ❌ Failed to assume role - skipping account
⚠️  Account ************ processing failed, continuing with next account...

🔍 Processing Account: ************ (AWS-account-networking) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountAuditRole
    ❌ Invalid JSON response from assume-role
  ❌ Failed to assume role - skipping account
⚠️  Account ************ processing failed, continuing with next account...

🔍 Processing Account: ************ (AWS-account-operations) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountAuditRole
    ❌ Invalid JSON response from assume-role
  ❌ Failed to assume role - skipping account
⚠️  Account ************ processing failed, continuing with next account...

🔍 Processing Account: ************ (AWS Account Canada SAAS) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountAuditRole
    ❌ Invalid JSON response from assume-role
  ❌ Failed to assume role - skipping account
⚠️  Account ************ processing failed, continuing with next account...

🔍 Processing Account: ************ (Incode Technologies T1  4717) - Status: ACTIVE
  (Management account - using current credentials)

📋 Listing IAM users in Account: ************ (Incode Technologies T1  4717) - Status: ACTIVE
    Found 0 IAM users
    No IAM users found in this account
⚠️  Account ************ processing failed, continuing with next account...

🔍 Processing Account: ************ (Operations) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountAuditRole
    ❌ Invalid JSON response from assume-role
  ❌ Failed to assume role - skipping account
⚠️  Account ************ processing failed, continuing with next account...

🔍 Processing Account: ************ (<EMAIL>) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountAuditRole
    ❌ Invalid JSON response from assume-role
  ❌ Failed to assume role - skipping account
⚠️  Account ************ processing failed, continuing with next account...

🔍 Processing Account: ************ (Metamap Staging) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountAuditRole
    ❌ Invalid JSON response from assume-role
  ❌ Failed to assume role - skipping account
⚠️  Account ************ processing failed, continuing with next account...

🔍 Processing Account: ************ (AWS-account-development) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountAuditRole
    ❌ Invalid JSON response from assume-role
  ❌ Failed to assume role - skipping account
⚠️  Account ************ processing failed, continuing with next account...

🔍 Processing Account: ************ (AWS-account-sre-mgmt) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountAuditRole
    ❌ Invalid JSON response from assume-role
  ❌ Failed to assume role - skipping account
⚠️  Account ************ processing failed, continuing with next account...

🔍 Processing Account: ************ (Microformas_Citi) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountAuditRole
    ❌ Invalid JSON response from assume-role
  ❌ Failed to assume role - skipping account
⚠️  Account ************ processing failed, continuing with next account...

🔍 Processing Account: ************ (AWS-account-omni-shared-services) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountAuditRole
    ❌ Invalid JSON response from assume-role
  ❌ Failed to assume role - skipping account
⚠️  Account ************ processing failed, continuing with next account...

🔍 Processing Account: ************ (aws-account-demo-k8s) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountAuditRole
    ❌ Invalid JSON response from assume-role
  ❌ Failed to assume role - skipping account
⚠️  Account ************ processing failed, continuing with next account...

🔍 Processing Account: ************ (<EMAIL>) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountAuditRole
    ❌ Invalid JSON response from assume-role
  ❌ Failed to assume role - skipping account
⚠️  Account ************ processing failed, continuing with next account...

🔍 Processing Account: ************ (aws-account-product-ops-tooling) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountAuditRole
    ❌ Invalid JSON response from assume-role
  ❌ Failed to assume role - skipping account
⚠️  Account ************ processing failed, continuing with next account...

🔍 Processing Account: ************ (AWS-account-filesharing) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountAuditRole
    ❌ Invalid JSON response from assume-role
  ❌ Failed to assume role - skipping account
⚠️  Account ************ processing failed, continuing with next account...

🔍 Processing Account: ************ (Metamap Development) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountAuditRole
    ❌ Invalid JSON response from assume-role
  ❌ Failed to assume role - skipping account
⚠️  Account ************ processing failed, continuing with next account...

🔍 Processing Account: ************ (<EMAIL>) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountAuditRole
    ❌ Invalid JSON response from assume-role
  ❌ Failed to assume role - skipping account
⚠️  Account ************ processing failed, continuing with next account...

🔍 Processing Account: ************ (<EMAIL>) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountAuditRole
    ❌ Invalid JSON response from assume-role
  ❌ Failed to assume role - skipping account
⚠️  Account ************ processing failed, continuing with next account...

🔍 Processing Account: ************ (<EMAIL>) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountAuditRole
    ❌ Invalid JSON response from assume-role
  ❌ Failed to assume role - skipping account
⚠️  Account ************ processing failed, continuing with next account...

🔍 Processing Account: ************ (<EMAIL>) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountAuditRole
    ❌ Invalid JSON response from assume-role
  ❌ Failed to assume role - skipping account
⚠️  Account ************ processing failed, continuing with next account...

🔍 Processing Account: ************ (<EMAIL>) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountAuditRole
    ❌ Invalid JSON response from assume-role
  ❌ Failed to assume role - skipping account
⚠️  Account ************ processing failed, continuing with next account...

🔍 Processing Account: ************ (Metamap Data) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountAuditRole
    ❌ Invalid JSON response from assume-role
  ❌ Failed to assume role - skipping account
⚠️  Account ************ processing failed, continuing with next account...

🔍 Processing Account: ************ (AWS-account-FL-HSVM-pilot) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountAuditRole
    ❌ Invalid JSON response from assume-role
  ❌ Failed to assume role - skipping account
⚠️  Account ************ processing failed, continuing with next account...

🔍 Processing Account: ************ (Athena Production) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountAuditRole
    ❌ Invalid JSON response from assume-role
  ❌ Failed to assume role - skipping account
⚠️  Account ************ processing failed, continuing with next account...

🔍 Processing Account: ************ (ct-log-audit) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountAuditRole
    ❌ Invalid JSON response from assume-role
  ❌ Failed to assume role - skipping account
⚠️  Account ************ processing failed, continuing with next account...

🔍 Processing Account: ************ (<EMAIL>) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountAuditRole
    ❌ Invalid JSON response from assume-role
  ❌ Failed to assume role - skipping account
⚠️  Account ************ processing failed, continuing with next account...

🔍 Processing Account: ************ (aws-account-citimx-dev) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountAuditRole
    ❌ Invalid JSON response from assume-role
  ❌ Failed to assume role - skipping account
⚠️  Account ************ processing failed, continuing with next account...

🔍 Processing Account: ************ (Metamap AI) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountAuditRole
    ❌ Invalid JSON response from assume-role
  ❌ Failed to assume role - skipping account
⚠️  Account ************ processing failed, continuing with next account...

🔍 Processing Account: ************ (ct-log-archive) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountAuditRole
    ❌ Invalid JSON response from assume-role
  ❌ Failed to assume role - skipping account
⚠️  Account ************ processing failed, continuing with next account...

🔍 Processing Account: ************ (Metamap Production) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountAuditRole
    ❌ Invalid JSON response from assume-role
  ❌ Failed to assume role - skipping account
⚠️  Account ************ processing failed, continuing with next account...

🔍 Processing Account: ************ (AWS-account-CA-DMV-pilot) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountAuditRole
    ❌ Invalid JSON response from assume-role
  ❌ Failed to assume role - skipping account
⚠️  Account ************ processing failed, continuing with next account...

🔍 Processing Account: ************ (CitiBanamex Analytics Account) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountAuditRole
    ❌ Invalid JSON response from assume-role
  ❌ Failed to assume role - skipping account
⚠️  Account ************ processing failed, continuing with next account...

🔍 Processing Account: ************ (AWS-account-demo) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountAuditRole
    ❌ Invalid JSON response from assume-role
  ❌ Failed to assume role - skipping account
⚠️  Account ************ processing failed, continuing with next account...

🔍 Processing Account: ************ (Athena Devel) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountAuditRole
    ❌ Invalid JSON response from assume-role
  ❌ Failed to assume role - skipping account
⚠️  Account ************ processing failed, continuing with next account...

🔍 Processing Account: ************ (AWS-account-pollid) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountAuditRole
    ❌ Invalid JSON response from assume-role
  ❌ Failed to assume role - skipping account
⚠️  Account ************ processing failed, continuing with next account...

🔍 Processing Account: ************ (AWS-account-georgia-dds-stage) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountAuditRole
    ❌ Invalid JSON response from assume-role
  ❌ Failed to assume role - skipping account
⚠️  Account ************ processing failed, continuing with next account...

🔍 Processing Account: ************ (aws-account-citimx-prod) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountAuditRole
    ❌ Invalid JSON response from assume-role
  ❌ Failed to assume role - skipping account
⚠️  Account ************ processing failed, continuing with next account...

🔍 Processing Account: ************ (<EMAIL>) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountAuditRole
    ❌ Invalid JSON response from assume-role
  ❌ Failed to assume role - skipping account
⚠️  Account ************ processing failed, continuing with next account...

🔍 Processing Account: ************ (aws-account-canada-k8s) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountAuditRole
    ❌ Invalid JSON response from assume-role
  ❌ Failed to assume role - skipping account
⚠️  Account ************ processing failed, continuing with next account...

🔍 Processing Account: ************ (AWS-account-saas-australia) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountAuditRole
    ❌ Invalid JSON response from assume-role
  ❌ Failed to assume role - skipping account
⚠️  Account ************ processing failed, continuing with next account...

🔍 Processing Account: ************ (AWS Account Colombia SOR) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountAuditRole
    ❌ Invalid JSON response from assume-role
  ❌ Failed to assume role - skipping account
⚠️  Account ************ processing failed, continuing with next account...

🔍 Processing Account: ************ (aws-account-ml) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountAuditRole
    ❌ Invalid JSON response from assume-role
  ❌ Failed to assume role - skipping account
⚠️  Account ************ processing failed, continuing with next account...

🔍 Processing Account: ************ (aws-account-labs) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountAuditRole
    ❌ Invalid JSON response from assume-role
  ❌ Failed to assume role - skipping account
⚠️  Account ************ processing failed, continuing with next account...

🔍 Processing Account: ************ (<EMAIL>) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountAuditRole
    ❌ Invalid JSON response from assume-role
  ❌ Failed to assume role - skipping account
⚠️  Account ************ processing failed, continuing with next account...

🔍 Processing Account: ************ (AWS-account-labelling-sandbox) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountAuditRole
    ❌ Invalid JSON response from assume-role
  ❌ Failed to assume role - skipping account
⚠️  Account ************ processing failed, continuing with next account...

🔍 Processing Account: ************ (aws-security) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountAuditRole
    ❌ Invalid JSON response from assume-role
  ❌ Failed to assume role - skipping account
⚠️  Account ************ processing failed, continuing with next account...

🔍 Processing Account: ************ (Mati Biometrics) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountAuditRole
    ❌ Invalid JSON response from assume-role
  ❌ Failed to assume role - skipping account
⚠️  Account ************ processing failed, continuing with next account...

🔍 Processing Account: ************ (incode_staging) - Status: ACTIVE
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountAuditRole
    ❌ Invalid JSON response from assume-role
  ❌ Failed to assume role - skipping account
⚠️  Account ************ processing failed, continuing with next account...
