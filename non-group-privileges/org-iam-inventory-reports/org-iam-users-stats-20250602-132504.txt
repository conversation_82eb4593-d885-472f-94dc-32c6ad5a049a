AWS Organization IAM Users Inventory Statistics
Generated: Mon Jun  2 13:26:03 CST 2025
Management Account: ************

=== ACCOUNT SUMMARY ===
Total Accounts in Organization: 57
Active Accounts: 55
Successfully Processed: 0
Failed to Access: 0

=== USER SUMMARY ===
Total IAM Users Found: 0

=== USER TYPE BREAKDOWN ===
GitHub Service Accounts: 0
Keycloak Service Accounts: 0
Terraform Service Accounts: 0
Other Service Accounts: 0
Human Users: 0
Other/Unknown: 0

=== FILES GENERATED ===
Detailed Report: /Users/<USER>/Incode/source/aws-stackset-inventory/non-group-privileges/org-iam-inventory-reports/org-iam-users-detailed-********-132504.txt
Summary CSV: /Users/<USER>/Incode/source/aws-stackset-inventory/non-group-privileges/org-iam-inventory-reports/org-iam-users-summary-********-132504.csv
Statistics: /Users/<USER>/Incode/source/aws-stackset-inventory/non-group-privileges/org-iam-inventory-reports/org-iam-users-stats-********-132504.txt
Accounts List: /Users/<USER>/Incode/source/aws-stackset-inventory/non-group-privileges/org-iam-inventory-reports/org-accounts-********-132504.txt

=== NEXT STEPS ===
1. Review the detailed report for complete user information
2. Analyze the summary CSV for patterns and compliance issues
3. Use the data for IAM governance and security reviews
4. Consider group-based access control migration where appropriate

